# GameFlex Cloudflare Infrastructure

This directory contains the Cloudflare infrastructure for GameFlex, including:

- **R2 Storage**: Object storage for images and videos across all environments
- **Workers**: AI-powered image processing with NSFW detection and video game recognition
- **Deployment Scripts**: Automated deployment across development, staging, and production

## Directory Structure

```
cloudflare/
├── README.md                    # This file
├── deploy.sh                    # Main deployment script
├── wrangler.toml               # Wrangler configuration
├── workers/                    # Cloudflare Workers
│   ├── image-processor/        # AI image processing worker
│   └── shared/                 # Shared utilities
├── r2/                         # R2 bucket configurations
│   ├── buckets.json           # Bucket definitions
│   └── cors.json              # CORS configuration
├── environments/              # Environment-specific configs
│   ├── development.env
│   ├── staging.env
│   └── production.env
└── scripts/                   # Utility scripts
    ├── setup-r2.sh
    ├── deploy-workers.sh
    └── cleanup.sh
```

## Environment Naming Convention

All resources follow the pattern: `gameflex-media-{environment}`

- **Development**: `gameflex-media-development`
- **Staging**: `gameflex-media-staging`
- **Production**: `gameflex-media-production`

## Quick Start

1. Install Wrangler CLI:
   ```bash
   npm install -g wrangler
   ```

2. Authenticate with Cloudflare:
   ```bash
   wrangler auth login
   ```

3. Deploy to development:
   ```bash
   ./deploy.sh development
   ```

## Features

### R2 Storage
- Separate buckets for each environment
- CORS configuration for web uploads
- Public access for media serving
- Lifecycle policies for cost optimization

### AI Image Processing
- **NSFW Detection**: Using Falconsai/nsfw_image_detection model
- **Video Game Recognition**: Using VideoGameBunny for game-specific tagging
- **AWS Integration**: Results stored for AWS backend retrieval

### Security
- Environment-specific API keys
- Secure token handling
- Rate limiting and abuse protection

## Deployment

See individual environment files in `environments/` for specific configuration details.

Run `./deploy.sh --help` for deployment options.
