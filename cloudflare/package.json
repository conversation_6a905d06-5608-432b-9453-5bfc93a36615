{"name": "gameflex-cloudflare-infrastructure", "version": "1.0.0", "description": "Cloudflare infrastructure for GameFlex media processing", "main": "workers/image-processor/src/index.ts", "scripts": {"build": "esbuild workers/image-processor/src/index.ts --bundle --outfile=dist/worker.js --format=esm --target=es2022", "dev": "wrangler dev --env development", "deploy:dev": "wrangler deploy --env development", "deploy:staging": "wrangler deploy --env staging", "deploy:prod": "wrangler deploy --env production", "test": "jest", "lint": "eslint workers/**/*.ts", "type-check": "tsc --noEmit", "setup:r2": "./scripts/setup-r2.sh", "cleanup": "./scripts/cleanup.sh"}, "dependencies": {"@cloudflare/workers-types": "^4.20240129.0", "@types/node": "^20.11.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "esbuild": "^0.19.11", "eslint": "^8.56.0", "jest": "^29.7.0", "typescript": "^5.3.3", "wrangler": "^3.25.0"}, "keywords": ["cloudflare", "workers", "r2", "ai", "image-processing", "nsfw-detection", "video-game-recognition"], "author": "GameFlex Team", "license": "MIT", "engines": {"node": ">=18.0.0"}}