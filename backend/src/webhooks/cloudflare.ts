import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, UpdateCommand, GetCommand } from '@aws-sdk/lib-dynamodb';
import { createHmac } from 'crypto';

// Configure AWS SDK for DynamoDB
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

const dynamodbClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamodbClient);

const MEDIA_TABLE = process.env.MEDIA_TABLE;
const POSTS_TABLE = process.env.POSTS_TABLE;
const WEBHOOK_SECRET = process.env.CLOUDFLARE_WEBHOOK_SECRET;

// Helper function to create response
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Verify webhook signature
const verifyWebhookSignature = (payload: string, signature: string, secret: string): boolean => {
    if (!signature || !secret) {
        return false;
    }

    const expectedSignature = createHmac('sha256', secret)
        .update(payload)
        .digest('hex');

    return signature === expectedSignature;
};

// Types for Cloudflare webhook payload
interface CloudflareWebhookPayload {
    event: 'media.processed' | 'media.uploaded' | 'media.failed';
    data: {
        mediaId: string;
        status: 'processing' | 'completed' | 'failed' | 'rejected_nsfw';
        nsfw_analysis?: {
            nsfw_score: number;
            is_nsfw: boolean;
            categories: {
                safe: number;
                suggestive: number;
                explicit: number;
            };
        };
        videogame_analysis?: {
            game_detected: boolean;
            game_title?: string;
            confidence: number;
            description: string;
            tags: string[];
            genre?: string;
        };
        clip_tagging?: {
            tags: string[];
            confidence_scores: number[];
            video_game_tags: string[];
            general_tags: string[];
        };
        metadata: {
            file_size: number;
            file_type: string;
            processed_at: string;
            processing_time_ms: number;
        };
        error?: string;
        rejection_reason?: string;
    };
    timestamp: string;
    environment: string;
}

// Handle media processing completion webhook
const handleMediaProcessed = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        console.log('CloudflareWebhook: Received media processed webhook');

        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        // Verify webhook signature
        const signature = event.headers['X-Webhook-Secret'] || event.headers['x-webhook-secret'];
        if (WEBHOOK_SECRET && !verifyWebhookSignature(event.body, signature, WEBHOOK_SECRET)) {
            console.error('CloudflareWebhook: Invalid webhook signature');
            return createResponse(401, { error: 'Invalid webhook signature' });
        }

        const payload: CloudflareWebhookPayload = JSON.parse(event.body);
        console.log('CloudflareWebhook: Parsed payload:', JSON.stringify(payload, null, 2));

        const { data } = payload;
        const { mediaId, status } = data;

        if (!mediaId) {
            return createResponse(400, { error: 'mediaId is required' });
        }

        // Get current media record
        const getCommand = new GetCommand({
            TableName: MEDIA_TABLE,
            Key: { id: mediaId }
        });

        const mediaResult = await dynamodb.send(getCommand);
        if (!mediaResult.Item) {
            console.error(`CloudflareWebhook: Media not found: ${mediaId}`);
            return createResponse(404, { error: 'Media not found' });
        }

        const mediaRecord = mediaResult.Item;
        console.log('CloudflareWebhook: Found media record:', JSON.stringify(mediaRecord, null, 2));

        // Prepare update based on processing status
        let updateExpression = 'SET #status = :status, updated_at = :updated_at';
        let expressionAttributeNames: any = { '#status': 'status' };
        let expressionAttributeValues: any = {
            ':status': status,
            ':updated_at': new Date().toISOString()
        };

        // Handle different processing outcomes
        if (status === 'rejected_nsfw') {
            // Mark as rejected and store rejection reason
            updateExpression += ', rejection_reason = :rejection_reason, nsfw_analysis = :nsfw_analysis';
            expressionAttributeValues[':rejection_reason'] = data.rejection_reason || 'Content flagged as NSFW';
            expressionAttributeValues[':nsfw_analysis'] = data.nsfw_analysis;

            console.log(`CloudflareWebhook: Media ${mediaId} rejected as NSFW`);
        } else if (status === 'completed') {
            // Store AI analysis results
            updateExpression += ', processing_completed_at = :processing_completed_at';
            expressionAttributeValues[':processing_completed_at'] = data.metadata.processed_at;

            if (data.nsfw_analysis) {
                updateExpression += ', nsfw_analysis = :nsfw_analysis';
                expressionAttributeValues[':nsfw_analysis'] = data.nsfw_analysis;
            }

            if (data.videogame_analysis) {
                updateExpression += ', videogame_analysis = :videogame_analysis';
                expressionAttributeValues[':videogame_analysis'] = data.videogame_analysis;
            }

            if (data.clip_tagging) {
                updateExpression += ', clip_tagging = :clip_tagging';
                expressionAttributeValues[':clip_tagging'] = data.clip_tagging;
            }

            // Store processing metadata
            updateExpression += ', processing_metadata = :processing_metadata';
            expressionAttributeValues[':processing_metadata'] = data.metadata;

            console.log(`CloudflareWebhook: Media ${mediaId} processing completed successfully`);
        } else if (status === 'failed') {
            // Store error information
            updateExpression += ', error_message = :error_message';
            expressionAttributeValues[':error_message'] = data.error || 'Processing failed';

            console.log(`CloudflareWebhook: Media ${mediaId} processing failed: ${data.error}`);
        }

        // Update media record
        const updateCommand = new UpdateCommand({
            TableName: MEDIA_TABLE,
            Key: { id: mediaId },
            UpdateExpression: updateExpression,
            ExpressionAttributeNames: expressionAttributeNames,
            ExpressionAttributeValues: expressionAttributeValues,
            ReturnValues: 'ALL_NEW'
        });

        const updateResult = await dynamodb.send(updateCommand);
        const updatedMedia = updateResult.Attributes;

        // If media is associated with a post, update post status
        if (updatedMedia && updatedMedia.postId) {
            await updatePostStatus(updatedMedia.postId, status, mediaId);
        }

        console.log('CloudflareWebhook: Media updated successfully:', JSON.stringify(updatedMedia, null, 2));

        return createResponse(200, {
            message: 'Media processing webhook handled successfully',
            mediaId,
            status,
            media: updatedMedia
        });

    } catch (error) {
        console.error('CloudflareWebhook: Error handling media processed webhook:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { 
            error: 'Failed to handle webhook', 
            details: errorMessage 
        });
    }
};

// Update post status based on media processing result
const updatePostStatus = async (postId: string, mediaStatus: string, mediaId: string): Promise<void> => {
    try {
        console.log(`CloudflareWebhook: Updating post ${postId} status based on media ${mediaId} status: ${mediaStatus}`);

        let postStatus: string;
        let updateExpression = 'SET #status = :status, updated_at = :updated_at';
        let expressionAttributeNames: any = { '#status': 'status' };
        let expressionAttributeValues: any = {
            ':updated_at': new Date().toISOString()
        };

        if (mediaStatus === 'rejected_nsfw') {
            postStatus = 'rejected';
            expressionAttributeValues[':status'] = postStatus;
        } else if (mediaStatus === 'completed') {
            postStatus = 'published';
            expressionAttributeValues[':status'] = postStatus;
            updateExpression += ', active = :active';
            expressionAttributeValues[':active'] = true;
        } else if (mediaStatus === 'failed') {
            postStatus = 'failed';
            expressionAttributeValues[':status'] = postStatus;
        } else {
            // For 'processing' status, keep post as is
            return;
        }

        const updateCommand = new UpdateCommand({
            TableName: POSTS_TABLE,
            Key: { id: postId },
            UpdateExpression: updateExpression,
            ExpressionAttributeNames: expressionAttributeNames,
            ExpressionAttributeValues: expressionAttributeValues
        });

        await dynamodb.send(updateCommand);
        console.log(`CloudflareWebhook: Post ${postId} status updated to ${postStatus}`);

    } catch (error) {
        console.error(`CloudflareWebhook: Error updating post ${postId} status:`, error);
        // Don't throw here as the main webhook processing succeeded
    }
};

// Main webhook handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('CloudflareWebhook: Received event:', JSON.stringify(event, null, 2));

    const { httpMethod, path } = event;

    try {
        // Handle CORS preflight
        if (httpMethod === 'OPTIONS') {
            return createResponse(200, { message: 'CORS preflight' });
        }

        if (httpMethod === 'POST' && path === '/webhooks/cloudflare/media-processed') {
            return await handleMediaProcessed(event);
        } else {
            return createResponse(404, { error: 'Webhook endpoint not found' });
        }

    } catch (error) {
        console.error('CloudflareWebhook: Handler error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return createResponse(500, { 
            error: 'Internal server error', 
            details: errorMessage 
        });
    }
};
