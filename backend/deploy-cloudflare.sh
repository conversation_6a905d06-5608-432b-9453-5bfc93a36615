#!/bin/bash

# GameFlex Cloudflare Deployment Script
# Deploys Cloudflare Workers and R2 infrastructure for GameFlex media processing
#
# Usage: ./deploy-cloudflare.sh [environment] [options]
# Environments: development, staging, production

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CLOUDFLARE_DIR="$SCRIPT_DIR/cloudflare"

# Default values
ENVIRONMENT=""
FORCE_DEPLOY=false
SKIP_R2_SETUP=false
SKIP_WORKERS=false
DRY_RUN=false
VERBOSE=false

# Print functions
print_header() {
    echo -e "${BLUE}============================================${NC}"
    echo -e "${BLUE}  GameFlex Cloudflare Deployment${NC}"
    echo -e "${BLUE}============================================${NC}"
    echo
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
GameFlex Cloudflare Deployment Script

USAGE:
    ./deploy-cloudflare.sh [ENVIRONMENT] [OPTIONS]

ENVIRONMENTS:
    development     Deploy to development environment (gameflex-media-development)
    staging         Deploy to staging environment (gameflex-media-staging)
    production      Deploy to production environment (gameflex-media-production)

OPTIONS:
    -h, --help              Show this help message
    -f, --force             Force deployment even if resources exist
    -n, --dry-run           Show what would be deployed without making changes
    -v, --verbose           Enable verbose output
    --skip-r2               Skip R2 bucket setup
    --skip-workers          Skip Workers deployment
    --workers-only          Deploy only Workers (skip R2 setup)
    --r2-only               Deploy only R2 buckets (skip Workers)

EXAMPLES:
    ./deploy-cloudflare.sh development
    ./deploy-cloudflare.sh staging --force
    ./deploy-cloudflare.sh production --dry-run
    ./deploy-cloudflare.sh development --workers-only

PREREQUISITES:
    - Wrangler CLI installed and authenticated
    - Node.js 18+ installed
    - Cloudflare account with Workers and R2 enabled
    - Required environment variables set

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            development|staging|production)
                ENVIRONMENT="$1"
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            -f|--force)
                FORCE_DEPLOY=true
                shift
                ;;
            -n|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            --skip-r2)
                SKIP_R2_SETUP=true
                shift
                ;;
            --skip-workers)
                SKIP_WORKERS=true
                shift
                ;;
            --workers-only)
                SKIP_R2_SETUP=true
                shift
                ;;
            --r2-only)
                SKIP_WORKERS=true
                shift
                ;;
            *)
                print_error "Unknown option: $1"
                echo "Use --help for usage information"
                exit 1
                ;;
        esac
    done

    # Validate environment
    if [[ -z "$ENVIRONMENT" ]]; then
        print_error "Environment is required"
        echo "Usage: $0 [development|staging|production] [options]"
        echo "Use --help for more information"
        exit 1
    fi
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."

    # Check if wrangler is installed
    if ! command -v wrangler &> /dev/null; then
        print_error "Wrangler CLI is not installed"
        print_status "Install with: npm install -g wrangler"
        exit 1
    fi

    # Check if authenticated with Cloudflare
    if ! wrangler whoami &> /dev/null; then
        print_error "Not authenticated with Cloudflare"
        print_status "Run: wrangler auth login"
        exit 1
    fi

    # Check if Node.js is installed
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed"
        exit 1
    fi

    # Check Node.js version
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [[ $NODE_VERSION -lt 18 ]]; then
        print_error "Node.js 18+ is required (current: $(node --version))"
        exit 1
    fi

    # Check if cloudflare directory exists
    if [[ ! -d "$CLOUDFLARE_DIR" ]]; then
        print_error "Cloudflare directory not found: $CLOUDFLARE_DIR"
        exit 1
    fi

    print_success "Prerequisites check passed"
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    cd "$CLOUDFLARE_DIR"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_status "[DRY RUN] Would run: npm install"
        return
    fi
    
    if [[ "$VERBOSE" == "true" ]]; then
        npm install
    else
        npm install --silent
    fi
    
    print_success "Dependencies installed"
}

# Setup R2 buckets
setup_r2_buckets() {
    if [[ "$SKIP_R2_SETUP" == "true" ]]; then
        print_status "Skipping R2 bucket setup"
        return
    fi

    print_status "Setting up R2 buckets for $ENVIRONMENT..."
    
    local bucket_name="gameflex-media-$ENVIRONMENT"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_status "[DRY RUN] Would create R2 bucket: $bucket_name"
        return
    fi
    
    cd "$CLOUDFLARE_DIR"
    
    # Run R2 setup script
    if [[ -f "scripts/setup-r2.sh" ]]; then
        chmod +x scripts/setup-r2.sh
        ./scripts/setup-r2.sh "$ENVIRONMENT" "$bucket_name"
    else
        print_warning "R2 setup script not found, creating bucket manually..."
        
        # Create bucket if it doesn't exist
        if ! wrangler r2 bucket list | grep -q "$bucket_name"; then
            print_status "Creating R2 bucket: $bucket_name"
            wrangler r2 bucket create "$bucket_name"
        else
            print_status "R2 bucket already exists: $bucket_name"
        fi
    fi
    
    print_success "R2 buckets configured for $ENVIRONMENT"
}

# Deploy Workers
deploy_workers() {
    if [[ "$SKIP_WORKERS" == "true" ]]; then
        print_status "Skipping Workers deployment"
        return
    fi

    print_status "Deploying Workers for $ENVIRONMENT..."
    
    cd "$CLOUDFLARE_DIR"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_status "[DRY RUN] Would deploy Workers to $ENVIRONMENT"
        return
    fi
    
    # Build the worker
    print_status "Building Worker..."
    npm run build
    
    # Deploy to the specified environment
    case $ENVIRONMENT in
        development)
            npm run deploy:dev
            ;;
        staging)
            npm run deploy:staging
            ;;
        production)
            npm run deploy:prod
            ;;
    esac
    
    print_success "Workers deployed to $ENVIRONMENT"
}

# Main deployment function
main() {
    print_header
    
    parse_args "$@"
    
    print_status "Deploying to environment: $ENVIRONMENT"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_warning "DRY RUN MODE - No actual changes will be made"
    fi
    
    check_prerequisites
    install_dependencies
    setup_r2_buckets
    deploy_workers
    
    print_success "Cloudflare deployment completed successfully!"
    print_status "Environment: $ENVIRONMENT"
    print_status "Bucket: gameflex-media-$ENVIRONMENT"
    print_status "Worker: gameflex-media-$ENVIRONMENT"
    
    echo
    print_status "Next steps:"
    print_status "1. Verify deployment in Cloudflare dashboard"
    print_status "2. Test image upload and processing"
    print_status "3. Update AWS backend configuration if needed"
}

# Run main function with all arguments
main "$@"
