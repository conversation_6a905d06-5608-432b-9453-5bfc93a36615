/**
 * Shared types for GameFlex Cloudflare Workers
 */

// Environment interface
export interface CloudflareEnv {
  MEDIA_BUCKET: R2Bucket;
  AI: Ai;
  ENVIRONMENT: string;
  NSFW_MODEL_NAME: string;
  VIDEOGAME_MODEL_NAME: string;
  CLIP_MODEL_NAME: string;
  AWS_REGION: string;
  AWS_DYNAMODB_TABLE: string;
  API_BASE_URL: string;
  WEBHOOK_SECRET: string;
  MAX_FILE_SIZE_MB: string;
  SUPPORTED_IMAGE_TYPES: string;
  RATE_LIMIT_REQUESTS_PER_MINUTE: string;
  ENABLE_NSFW_DETECTION: string;
  ENABLE_VIDEOGAME_RECOGNITION: string;
  DEBUG_MODE: string;
}

// AI Model Response Types
export interface NSFWDetectionResult {
  nsfw_score: number;
  is_nsfw: boolean;
  categories: {
    safe: number;
    suggestive: number;
    explicit: number;
  };
  confidence?: number;
}

export interface VideoGameRecognitionResult {
  game_detected: boolean;
  game_title?: string;
  confidence: number;
  description: string;
  tags: string[];
  genre?: string;
  platform?: string;
  release_year?: number;
}

export interface CLIPTaggingResult {
  tags: string[];
  confidence_scores: number[];
  video_game_tags: string[];
  general_tags: string[];
}

// Processing Results
export interface ProcessingResult {
  mediaId: string;
  status: 'processing' | 'completed' | 'failed' | 'rejected_nsfw';
  nsfw_analysis?: NSFWDetectionResult;
  videogame_analysis?: VideoGameRecognitionResult;
  clip_tagging?: CLIPTaggingResult;
  metadata: MediaMetadata;
  error?: string;
  rejection_reason?: string;
}

export interface MediaMetadata {
  file_size: number;
  file_type: string;
  dimensions?: {
    width: number;
    height: number;
  };
  duration?: number; // for videos
  processed_at: string;
  processing_time_ms: number;
  checksum?: string;
}

// Upload Types
export interface UploadUrlRequest {
  mediaId: string;
  userId: string;
  fileName: string;
  fileType: string;
  fileSize?: number;
  mediaType?: 'image' | 'video' | 'avatar' | 'reflex';
}

export interface UploadUrlResponse {
  success: boolean;
  mediaId: string;
  uploadUrl: string;
  r2Key: string;
  publicUrl: string;
  expiresIn: number;
  metadata: any;
}

export interface ProcessRequest {
  mediaId: string;
  r2Key?: string;
}

// Webhook Types
export interface WebhookPayload {
  event: 'media.processed' | 'media.uploaded' | 'media.failed';
  data: ProcessingResult | UploadResponse;
  timestamp: string;
  environment: string;
}

// Error Types
export interface APIError {
  error: string;
  message?: string;
  details?: string;
  code?: string;
}

// Rate Limiting
export interface RateLimitInfo {
  requests_remaining: number;
  reset_time: number;
  limit: number;
}

// File Validation
export interface FileValidationResult {
  valid: boolean;
  error?: string;
  warnings?: string[];
}

// Storage Types
export interface R2ObjectMetadata {
  userId: string;
  mediaId: string;
  originalName: string;
  uploadedAt: string;
  processedAt?: string;
  mediaType?: string;
}

// Analytics Types
export interface ProcessingAnalytics {
  total_processed: number;
  nsfw_detected: number;
  games_detected: number;
  average_processing_time: number;
  errors: number;
  environment: string;
  date: string;
}

// Configuration Types
export interface WorkerConfig {
  max_file_size_mb: number;
  supported_image_types: string[];
  supported_video_types: string[];
  rate_limit_per_minute: number;
  enable_nsfw_detection: boolean;
  enable_videogame_recognition: boolean;
  enable_thumbnails: boolean;
  debug_mode: boolean;
}

// Constants
export const SUPPORTED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/webp',
  'image/gif'
] as const;

export const SUPPORTED_VIDEO_TYPES = [
  'video/mp4',
  'video/mov',
  'video/avi',
  'video/quicktime'
] as const;

export const MEDIA_FOLDERS = {
  MEDIA: 'media',
  AVATARS: 'avatars',
  REFLEXES: 'reflexes',
  TEMP: 'temp',
  PROCESSING: 'processing',
  THUMBNAILS: 'thumbnails',
  METADATA: 'metadata'
} as const;

export const ENVIRONMENTS = ['development', 'staging', 'production'] as const;
export type Environment = typeof ENVIRONMENTS[number];

// Type guards
export function isValidEnvironment(env: string): env is Environment {
  return ENVIRONMENTS.includes(env as Environment);
}

export function isImageType(mimeType: string): boolean {
  return SUPPORTED_IMAGE_TYPES.includes(mimeType as any);
}

export function isVideoType(mimeType: string): boolean {
  return SUPPORTED_VIDEO_TYPES.includes(mimeType as any);
}
