/**
 * Shared utilities for GameFlex Cloudflare Workers
 */

import { 
  CloudflareEnv, 
  FileValidationResult, 
  APIError,
  RateLimitInfo,
  SUPPORTED_IMAGE_TYPES,
  SUPPORTED_VIDEO_TYPES,
  isImageType,
  isVideoType
} from './types';

/**
 * Generate a standardized API response
 */
export function createAPIResponse(
  data: any, 
  status: number = 200, 
  headers: Record<string, string> = {}
): Response {
  const defaultHeaders = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    ...headers
  };

  return new Response(JSON.stringify(data), {
    status,
    headers: defaultHeaders
  });
}

/**
 * Create error response
 */
export function createErrorResponse(
  error: string, 
  status: number = 400, 
  details?: string
): Response {
  const errorData: APIError = {
    error,
    details
  };

  return createAPIResponse(errorData, status);
}

/**
 * Validate file upload
 */
export function validateFile(file: File, env: CloudflareEnv): FileValidationResult {
  const warnings: string[] = [];

  // Check file size
  const maxSizeMB = parseInt(env.MAX_FILE_SIZE_MB) || 50;
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  
  if (file.size > maxSizeBytes) {
    return {
      valid: false,
      error: `File size exceeds ${maxSizeMB}MB limit (${(file.size / 1024 / 1024).toFixed(2)}MB)`
    };
  }

  // Check if file is empty
  if (file.size === 0) {
    return {
      valid: false,
      error: 'File is empty'
    };
  }

  // Check file type
  const supportedTypes = env.SUPPORTED_IMAGE_TYPES?.split(',') || SUPPORTED_IMAGE_TYPES;
  
  if (!supportedTypes.includes(file.type)) {
    return {
      valid: false,
      error: `Unsupported file type: ${file.type}. Supported types: ${supportedTypes.join(', ')}`
    };
  }

  // Add warnings for large files
  if (file.size > 10 * 1024 * 1024) { // 10MB
    warnings.push('Large file size may result in slower processing');
  }

  // Check file extension matches MIME type
  const extension = getFileExtension(file.name);
  const expectedMimeTypes = getExpectedMimeTypes(extension);
  
  if (expectedMimeTypes.length > 0 && !expectedMimeTypes.includes(file.type)) {
    warnings.push(`File extension (${extension}) may not match content type (${file.type})`);
  }

  return { 
    valid: true, 
    warnings: warnings.length > 0 ? warnings : undefined 
  };
}

/**
 * Get file extension from filename
 */
export function getFileExtension(filename: string): string {
  return filename.split('.').pop()?.toLowerCase() || '';
}

/**
 * Get expected MIME types for file extension
 */
function getExpectedMimeTypes(extension: string): string[] {
  const mimeMap: Record<string, string[]> = {
    'jpg': ['image/jpeg'],
    'jpeg': ['image/jpeg'],
    'png': ['image/png'],
    'webp': ['image/webp'],
    'gif': ['image/gif'],
    'mp4': ['video/mp4'],
    'mov': ['video/mov', 'video/quicktime'],
    'avi': ['video/avi', 'video/x-msvideo']
  };

  return mimeMap[extension] || [];
}

/**
 * Generate R2 key for uploaded file
 */
export function generateR2Key(
  userId: string, 
  mediaId: string, 
  filename: string, 
  mediaType: string = 'media'
): string {
  const extension = getFileExtension(filename);
  
  switch (mediaType) {
    case 'avatar':
      return `avatars/${userId}/${mediaId}.${extension}`;
    case 'reflex':
      return `reflexes/${userId}/${mediaId}.${extension}`;
    case 'video':
      return `videos/${userId}/${mediaId}.${extension}`;
    default:
      return `media/${userId}/${mediaId}.${extension}`;
  }
}

/**
 * Generate public URL for R2 object
 */
export function generatePublicURL(bucketName: string, key: string, customDomain?: string): string {
  if (customDomain) {
    return `${customDomain}/${key}`;
  }
  
  // Default R2 public URL format
  return `https://${bucketName}.r2.dev/${key}`;
}

/**
 * Parse and validate environment variables
 */
export function parseEnvironmentConfig(env: CloudflareEnv) {
  return {
    maxFileSizeMB: parseInt(env.MAX_FILE_SIZE_MB) || 50,
    supportedImageTypes: env.SUPPORTED_IMAGE_TYPES?.split(',') || SUPPORTED_IMAGE_TYPES,
    rateLimitPerMinute: parseInt(env.RATE_LIMIT_REQUESTS_PER_MINUTE) || 100,
    enableNSFWDetection: env.ENABLE_NSFW_DETECTION === 'true',
    enableVideoGameRecognition: env.ENABLE_VIDEOGAME_RECOGNITION === 'true',
    debugMode: env.DEBUG_MODE === 'true',
    environment: env.ENVIRONMENT || 'development'
  };
}

/**
 * Simple rate limiting using Cloudflare's built-in rate limiting
 */
export async function checkRateLimit(
  request: Request, 
  env: CloudflareEnv
): Promise<{ allowed: boolean; info: RateLimitInfo }> {
  // This is a simplified rate limiting implementation
  // In production, you might want to use Cloudflare's Rate Limiting API
  // or implement more sophisticated rate limiting with Durable Objects
  
  const rateLimitPerMinute = parseInt(env.RATE_LIMIT_REQUESTS_PER_MINUTE) || 100;
  const clientIP = request.headers.get('CF-Connecting-IP') || 'unknown';
  
  // For now, return allowed - implement actual rate limiting as needed
  return {
    allowed: true,
    info: {
      requests_remaining: rateLimitPerMinute,
      reset_time: Date.now() + 60000, // 1 minute from now
      limit: rateLimitPerMinute
    }
  };
}

/**
 * Log with structured format
 */
export function logInfo(message: string, data?: any, env?: CloudflareEnv) {
  const logEntry = {
    timestamp: new Date().toISOString(),
    level: 'INFO',
    message,
    environment: env?.ENVIRONMENT || 'unknown',
    data
  };
  
  console.log(JSON.stringify(logEntry));
}

export function logError(message: string, error?: Error, data?: any, env?: CloudflareEnv) {
  const logEntry = {
    timestamp: new Date().toISOString(),
    level: 'ERROR',
    message,
    environment: env?.ENVIRONMENT || 'unknown',
    error: error ? {
      name: error.name,
      message: error.message,
      stack: env?.DEBUG_MODE === 'true' ? error.stack : undefined
    } : undefined,
    data
  };
  
  console.error(JSON.stringify(logEntry));
}

/**
 * Generate unique ID (simple implementation)
 */
export function generateId(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
}

/**
 * Calculate file checksum (simple hash)
 */
export async function calculateChecksum(data: ArrayBuffer): Promise<string> {
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}

/**
 * Sanitize filename for storage
 */
export function sanitizeFilename(filename: string): string {
  return filename
    .replace(/[^a-zA-Z0-9.-]/g, '_')
    .replace(/_{2,}/g, '_')
    .toLowerCase();
}

/**
 * Get image dimensions from buffer (basic implementation)
 */
export function getImageDimensions(buffer: ArrayBuffer, mimeType: string): { width: number; height: number } | null {
  // This is a simplified implementation
  // In a real scenario, you'd want to use a proper image parsing library
  // For now, return null and implement proper image dimension detection as needed
  return null;
}
