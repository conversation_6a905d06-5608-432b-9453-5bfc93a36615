/**
 * GameFlex Image Processing Worker
 * 
 * Handles image uploads, AI processing, and metadata storage
 * Integrates with Falconsai/nsfw_image_detection and VideoGameBunny models
 */

export interface Env {
  // R2 Bucket binding
  MEDIA_BUCKET: R2Bucket;

  // AI binding for Cloudflare AI models
  AI: Ai;

  // Environment variables
  ENVIRONMENT: string;
  NSFW_MODEL_NAME: string;
  VIDEOGAME_MODEL_NAME: string;
  AWS_REGION: string;
  AWS_DYNAMODB_TABLE: string;
  API_BASE_URL: string;
  WEBHOOK_SECRET: string;
  MAX_FILE_SIZE_MB: string;
  SUPPORTED_IMAGE_TYPES: string;
  RATE_LIMIT_REQUESTS_PER_MINUTE: string;
  ENABLE_NSFW_DETECTION: string;
  ENABLE_VIDEOGAME_RECOGNITION: string;
  DEBUG_MODE: string;
}

// Types for AI model responses
interface NSFWDetectionResult {
  nsfw_score: number;
  is_nsfw: boolean;
  categories: {
    safe: number;
    suggestive: number;
    explicit: number;
  };
}

interface VideoGameRecognitionResult {
  game_detected: boolean;
  game_title?: string;
  confidence: number;
  description: string;
  tags: string[];
  genre?: string;
}

interface ProcessingResult {
  mediaId: string;
  status: 'processing' | 'completed' | 'failed';
  nsfw_analysis?: NSFWDetectionResult;
  videogame_analysis?: VideoGameRecognitionResult;
  metadata: {
    file_size: number;
    file_type: string;
    dimensions?: { width: number; height: number };
    processed_at: string;
    processing_time_ms: number;
  };
  error?: string;
}

export default {
  async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
    // Handle CORS preflight requests
    if (request.method === 'OPTIONS') {
      return handleCORS(request);
    }

    try {
      const url = new URL(request.url);
      const path = url.pathname;

      // Route requests
      switch (true) {
        case path === '/upload' && request.method === 'POST':
          return await handleUpload(request, env);

        case path === '/process' && request.method === 'POST':
          return await handleProcess(request, env);

        case path.startsWith('/media/') && request.method === 'GET':
          return await handleMediaGet(request, env);

        case path === '/health' && request.method === 'GET':
          return new Response(JSON.stringify({
            status: 'healthy',
            environment: env.ENVIRONMENT,
            timestamp: new Date().toISOString()
          }), {
            headers: { 'Content-Type': 'application/json' }
          });

        default:
          return new Response('Not Found', { status: 404 });
      }
    } catch (error) {
      console.error('Worker error:', error);
      return new Response(JSON.stringify({
        error: 'Internal Server Error',
        message: env.DEBUG_MODE === 'true' ? (error as Error).message : 'An error occurred'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  },

  // Scheduled handler for cleanup tasks
  async scheduled(event: ScheduledEvent, env: Env, ctx: ExecutionContext): Promise<void> {
    console.log('Running scheduled cleanup task');
    await cleanupTempFiles(env);
  }
};

/**
 * Handle CORS preflight requests
 */
function handleCORS(request: Request): Response {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Max-Age': '86400',
  };

  return new Response(null, {
    status: 204,
    headers: corsHeaders
  });
}

/**
 * Handle file upload to R2
 */
async function handleUpload(request: Request, env: Env): Promise<Response> {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const mediaId = formData.get('mediaId') as string;
    const userId = formData.get('userId') as string;

    if (!file || !mediaId || !userId) {
      return new Response(JSON.stringify({
        error: 'Missing required fields: file, mediaId, userId'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Validate file type and size
    const validation = validateFile(file, env);
    if (!validation.valid) {
      return new Response(JSON.stringify({ error: validation.error }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Generate R2 key
    const r2Key = `media/${userId}/${mediaId}.${getFileExtension(file.name)}`;

    // Upload to R2
    await env.MEDIA_BUCKET.put(r2Key, file.stream(), {
      httpMetadata: {
        contentType: file.type,
        contentLength: file.size.toString(),
      },
      customMetadata: {
        userId,
        mediaId,
        originalName: file.name,
        uploadedAt: new Date().toISOString(),
      }
    });

    // Trigger processing
    const processingResult = await processImage(file, mediaId, userId, env);

    return new Response(JSON.stringify({
      success: true,
      mediaId,
      r2Key,
      url: `https://${env.MEDIA_BUCKET.name}.r2.dev/${r2Key}`,
      processing: processingResult
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });

  } catch (error) {
    console.error('Upload error:', error);
    return new Response(JSON.stringify({
      error: 'Upload failed',
      details: env.DEBUG_MODE === 'true' ? (error as Error).message : undefined
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * Handle image processing request
 */
async function handleProcess(request: Request, env: Env): Promise<Response> {
  try {
    const { mediaId, r2Key } = await request.json();

    if (!mediaId || !r2Key) {
      return new Response(JSON.stringify({
        error: 'Missing required fields: mediaId, r2Key'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get file from R2
    const object = await env.MEDIA_BUCKET.get(r2Key);
    if (!object) {
      return new Response(JSON.stringify({ error: 'File not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Process the image
    const file = new File([await object.arrayBuffer()], 'image', {
      type: object.httpMetadata?.contentType || 'image/jpeg'
    });

    const result = await processImage(file, mediaId, '', env);

    return new Response(JSON.stringify(result), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });

  } catch (error) {
    console.error('Processing error:', error);
    return new Response(JSON.stringify({
      error: 'Processing failed',
      details: env.DEBUG_MODE === 'true' ? (error as Error).message : undefined
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * Handle media file retrieval
 */
async function handleMediaGet(request: Request, env: Env): Promise<Response> {
  try {
    const url = new URL(request.url);
    const key = url.pathname.replace('/media/', '');

    const object = await env.MEDIA_BUCKET.get(key);
    if (!object) {
      return new Response('File not found', { status: 404 });
    }

    const headers = new Headers();
    object.writeHttpMetadata(headers);
    headers.set('etag', object.httpEtag);
    headers.set('Access-Control-Allow-Origin', '*');

    return new Response(object.body, { headers });

  } catch (error) {
    console.error('Media get error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}

/**
 * Process image with AI models
 */
async function processImage(file: File, mediaId: string, userId: string, env: Env): Promise<ProcessingResult> {
  const startTime = Date.now();

  try {
    const result: ProcessingResult = {
      mediaId,
      status: 'processing',
      metadata: {
        file_size: file.size,
        file_type: file.type,
        processed_at: new Date().toISOString(),
        processing_time_ms: 0
      }
    };

    // Convert file to array buffer for AI processing
    const imageBuffer = await file.arrayBuffer();
    const imageArray = new Uint8Array(imageBuffer);

    // Run NSFW detection if enabled
    if (env.ENABLE_NSFW_DETECTION === 'true') {
      try {
        const nsfwResult = await env.AI.run(env.NSFW_MODEL_NAME, {
          image: imageArray
        }) as NSFWDetectionResult;

        result.nsfw_analysis = nsfwResult;
      } catch (error) {
        console.error('NSFW detection failed:', error);
        if (env.DEBUG_MODE === 'true') {
          result.error = `NSFW detection failed: ${(error as Error).message}`;
        }
      }
    }

    // Run video game recognition if enabled
    if (env.ENABLE_VIDEOGAME_RECOGNITION === 'true') {
      try {
        const gameResult = await env.AI.run(env.VIDEOGAME_MODEL_NAME, {
          image: imageArray
        }) as VideoGameRecognitionResult;

        result.videogame_analysis = gameResult;
      } catch (error) {
        console.error('Video game recognition failed:', error);
        if (env.DEBUG_MODE === 'true') {
          result.error = result.error ?
            `${result.error}; Game recognition failed: ${(error as Error).message}` :
            `Game recognition failed: ${(error as Error).message}`;
        }
      }
    }

    // Calculate processing time
    result.metadata.processing_time_ms = Date.now() - startTime;
    result.status = 'completed';

    // Store results for AWS backend retrieval
    await storeProcessingResults(result, env);

    return result;

  } catch (error) {
    console.error('Image processing failed:', error);
    return {
      mediaId,
      status: 'failed',
      metadata: {
        file_size: file.size,
        file_type: file.type,
        processed_at: new Date().toISOString(),
        processing_time_ms: Date.now() - startTime
      },
      error: env.DEBUG_MODE === 'true' ? (error as Error).message : 'Processing failed'
    };
  }
}

/**
 * Validate uploaded file
 */
function validateFile(file: File, env: Env): { valid: boolean; error?: string } {
  // Check file size
  const maxSizeMB = parseInt(env.MAX_FILE_SIZE_MB) || 50;
  const maxSizeBytes = maxSizeMB * 1024 * 1024;

  if (file.size > maxSizeBytes) {
    return {
      valid: false,
      error: `File size exceeds ${maxSizeMB}MB limit`
    };
  }

  // Check file type
  const supportedTypes = env.SUPPORTED_IMAGE_TYPES?.split(',') || [
    'image/jpeg', 'image/png', 'image/webp', 'image/gif'
  ];

  if (!supportedTypes.includes(file.type)) {
    return {
      valid: false,
      error: `Unsupported file type. Supported types: ${supportedTypes.join(', ')}`
    };
  }

  return { valid: true };
}

/**
 * Get file extension from filename
 */
function getFileExtension(filename: string): string {
  return filename.split('.').pop()?.toLowerCase() || 'jpg';
}

/**
 * Store processing results for AWS backend retrieval
 */
async function storeProcessingResults(result: ProcessingResult, env: Env): Promise<void> {
  try {
    // Store as JSON in R2 metadata bucket
    const metadataKey = `metadata/${result.mediaId}.json`;

    await env.MEDIA_BUCKET.put(metadataKey, JSON.stringify(result, null, 2), {
      httpMetadata: {
        contentType: 'application/json',
      },
      customMetadata: {
        type: 'processing_result',
        mediaId: result.mediaId,
        status: result.status,
        createdAt: new Date().toISOString(),
      }
    });

    // Optionally send webhook to AWS backend
    if (env.API_BASE_URL && env.WEBHOOK_SECRET) {
      await sendWebhook(result, env);
    }

  } catch (error) {
    console.error('Failed to store processing results:', error);
    // Don't throw here as the main processing succeeded
  }
}

/**
 * Send webhook notification to AWS backend
 */
async function sendWebhook(result: ProcessingResult, env: Env): Promise<void> {
  try {
    const webhookUrl = `${env.API_BASE_URL}/webhooks/cloudflare/media-processed`;

    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Webhook-Secret': env.WEBHOOK_SECRET,
        'User-Agent': 'GameFlex-Cloudflare-Worker/1.0'
      },
      body: JSON.stringify({
        event: 'media.processed',
        data: result,
        timestamp: new Date().toISOString(),
        environment: env.ENVIRONMENT
      })
    });

    if (!response.ok) {
      console.error('Webhook failed:', response.status, await response.text());
    }

  } catch (error) {
    console.error('Webhook error:', error);
    // Don't throw here as it's not critical
  }
}

/**
 * Cleanup temporary files (scheduled task)
 */
async function cleanupTempFiles(env: Env): Promise<void> {
  try {
    console.log('Starting cleanup of temporary files');

    // List objects in temp/ folder
    const tempObjects = await env.MEDIA_BUCKET.list({ prefix: 'temp/' });

    const cutoffTime = new Date();
    cutoffTime.setHours(cutoffTime.getHours() - 24); // 24 hours ago

    let deletedCount = 0;

    for (const object of tempObjects.objects) {
      if (object.uploaded && object.uploaded < cutoffTime) {
        await env.MEDIA_BUCKET.delete(object.key);
        deletedCount++;
      }
    }

    console.log(`Cleanup completed: deleted ${deletedCount} temporary files`);

  } catch (error) {
    console.error('Cleanup failed:', error);
  }
}
