/**
 * GameFlex Image Processing Worker
 *
 * Handles image uploads, AI processing, and metadata storage
 * Integrates with Falconsai/nsfw_image_detection, VideoGameBunny, and CLIP models
 */

// Import Cloudflare Worker types
/// <reference types="@cloudflare/workers-types" />

export interface Env {
  // R2 Bucket binding
  MEDIA_BUCKET: R2Bucket;

  // AI binding for Cloudflare AI models
  AI: Ai;

  // Environment variables
  ENVIRONMENT: string;
  NSFW_MODEL_NAME: string;
  VIDEOGAME_MODEL_NAME: string;
  CLIP_MODEL_NAME: string;
  AWS_REGION: string;
  AWS_DYNAMODB_TABLE: string;
  API_BASE_URL: string;
  WEBHOOK_SECRET: string;
  MAX_FILE_SIZE_MB: string;
  SUPPORTED_IMAGE_TYPES: string;
  RATE_LIMIT_REQUESTS_PER_MINUTE: string;
  ENABLE_NSFW_DETECTION: string;
  ENABLE_VIDEOGAME_RECOGNITION: string;
  DEBUG_MODE: string;
}

// Types for AI model responses
interface NSFWDetectionResult {
  nsfw_score: number;
  is_nsfw: boolean;
  categories: {
    safe: number;
    suggestive: number;
    explicit: number;
  };
}

interface VideoGameRecognitionResult {
  game_detected: boolean;
  game_title?: string;
  confidence: number;
  description: string;
  tags: string[];
  genre?: string;
}

interface CLIPTaggingResult {
  tags: string[];
  confidence_scores: number[];
  video_game_tags: string[];
  general_tags: string[];
}

interface ProcessingResult {
  mediaId: string;
  status: 'processing' | 'completed' | 'failed' | 'rejected_nsfw';
  nsfw_analysis?: NSFWDetectionResult;
  videogame_analysis?: VideoGameRecognitionResult;
  clip_tagging?: CLIPTaggingResult;
  metadata: {
    file_size: number;
    file_type: string;
    dimensions?: { width: number; height: number };
    processed_at: string;
    processing_time_ms: number;
  };
  error?: string;
  rejection_reason?: string;
}

export default {
  async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
    // Handle CORS preflight requests
    if (request.method === 'OPTIONS') {
      return handleCORS(request);
    }

    try {
      const url = new URL(request.url);
      const path = url.pathname;

      // Route requests
      switch (true) {
        case path === '/upload-url' && request.method === 'POST':
          return await handleGetUploadUrl(request, env);

        case path === '/process' && request.method === 'POST':
          return await handleProcess(request, env);

        case path.startsWith('/media/') && request.method === 'GET':
          return await handleMediaGet(request, env);

        case path === '/health' && request.method === 'GET':
          return new Response(JSON.stringify({
            status: 'healthy',
            environment: env.ENVIRONMENT,
            timestamp: new Date().toISOString()
          }), {
            headers: { 'Content-Type': 'application/json' }
          });

        default:
          return new Response('Not Found', { status: 404 });
      }
    } catch (error) {
      console.error('Worker error:', error);
      return new Response(JSON.stringify({
        error: 'Internal Server Error',
        message: env.DEBUG_MODE === 'true' ? (error as Error).message : 'An error occurred'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  },

  // Scheduled handler for cleanup tasks
  async scheduled(event: ScheduledEvent, env: Env, ctx: ExecutionContext): Promise<void> {
    console.log('Running scheduled cleanup task');
    await cleanupTempFiles(env);
  }
};

/**
 * Handle CORS preflight requests
 */
function handleCORS(request: Request): Response {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Max-Age': '86400',
  };

  return new Response(null, {
    status: 204,
    headers: corsHeaders
  });
}

/**
 * Generate presigned upload URL for direct R2 upload
 */
async function handleGetUploadUrl(request: Request, env: Env): Promise<Response> {
  try {
    const body = await request.json();
    const { mediaId, userId, fileName, fileType, fileSize, mediaType = 'media' } = body;

    if (!mediaId || !userId || !fileName || !fileType) {
      return new Response(JSON.stringify({
        error: 'Missing required fields: mediaId, userId, fileName, fileType'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Validate file type and size
    const validation = validateFileMetadata(fileType, fileSize, env);
    if (!validation.valid) {
      return new Response(JSON.stringify({ error: validation.error }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Generate R2 key
    const r2Key = generateR2Key(userId, mediaId, fileName, mediaType);

    // Generate presigned URL for upload (valid for 10 minutes)
    const uploadUrl = await env.MEDIA_BUCKET.createMultipartUpload(r2Key);

    // Store metadata for later processing
    const metadata = {
      mediaId,
      userId,
      fileName,
      fileType,
      fileSize: fileSize || 0,
      mediaType,
      r2Key,
      uploadedAt: new Date().toISOString(),
      status: 'pending_upload'
    };

    // Store metadata in R2 for tracking
    await env.MEDIA_BUCKET.put(`metadata/pending/${mediaId}.json`, JSON.stringify(metadata), {
      httpMetadata: {
        contentType: 'application/json',
      }
    });

    // Generate public URL for later access
    const publicUrl = `https://${env.R2_BUCKET_NAME}.r2.dev/${r2Key}`;

    return new Response(JSON.stringify({
      success: true,
      mediaId,
      uploadUrl: uploadUrl.uploadId, // This would be the actual presigned URL in practice
      r2Key,
      publicUrl,
      expiresIn: 600, // 10 minutes
      metadata
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });

  } catch (error) {
    console.error('Upload URL generation error:', error);
    return new Response(JSON.stringify({
      error: 'Failed to generate upload URL',
      details: env.DEBUG_MODE === 'true' ? (error as Error).message : undefined
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * Handle image processing request for already uploaded files
 */
async function handleProcess(request: Request, env: Env): Promise<Response> {
  try {
    const body = await request.json() as { mediaId: string; r2Key?: string };
    const { mediaId, r2Key } = body;

    if (!mediaId) {
      return new Response(JSON.stringify({
        error: 'Missing required field: mediaId'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    let objectKey = r2Key;
    let metadata: any = null;

    // If no r2Key provided, try to get it from metadata
    if (!objectKey) {
      try {
        const metadataObject = await env.MEDIA_BUCKET.get(`metadata/pending/${mediaId}.json`);
        if (metadataObject) {
          metadata = JSON.parse(await metadataObject.text());
          objectKey = metadata.r2Key;
        }
      } catch (error) {
        console.error('Failed to get metadata:', error);
      }
    }

    if (!objectKey) {
      return new Response(JSON.stringify({
        error: 'File not found - no r2Key provided and no metadata found'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get file from R2
    const object = await env.MEDIA_BUCKET.get(objectKey);
    if (!object) {
      return new Response(JSON.stringify({
        error: `File not found at key: ${objectKey}`
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get file metadata
    const fileSize = object.size;
    const fileType = object.httpMetadata?.contentType || 'image/jpeg';
    const userId = metadata?.userId || object.customMetadata?.userId || 'unknown';

    // Process the image with AI models
    const result = await processImageFromR2(object, mediaId, userId, fileSize, fileType, env);

    // Update metadata status
    if (metadata) {
      metadata.status = result.status;
      metadata.processedAt = new Date().toISOString();
      await env.MEDIA_BUCKET.put(`metadata/processed/${mediaId}.json`, JSON.stringify(metadata));

      // Remove from pending
      await env.MEDIA_BUCKET.delete(`metadata/pending/${mediaId}.json`);
    }

    return new Response(JSON.stringify(result), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });

  } catch (error) {
    console.error('Processing error:', error);
    return new Response(JSON.stringify({
      error: 'Processing failed',
      details: env.DEBUG_MODE === 'true' ? (error as Error).message : undefined
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * Handle media file retrieval
 */
async function handleMediaGet(request: Request, env: Env): Promise<Response> {
  try {
    const url = new URL(request.url);
    const key = url.pathname.replace('/media/', '');

    const object = await env.MEDIA_BUCKET.get(key);
    if (!object) {
      return new Response('File not found', { status: 404 });
    }

    const headers = new Headers();
    object.writeHttpMetadata(headers);
    headers.set('etag', object.httpEtag);
    headers.set('Access-Control-Allow-Origin', '*');

    return new Response(object.body, { headers });

  } catch (error) {
    console.error('Media get error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}

/**
 * Process image from R2 object with AI models
 */
async function processImageFromR2(
  r2Object: any,
  mediaId: string,
  userId: string,
  fileSize: number,
  fileType: string,
  env: Env
): Promise<ProcessingResult> {
  const startTime = Date.now();

  try {
    const result: ProcessingResult = {
      mediaId,
      status: 'processing',
      metadata: {
        file_size: fileSize,
        file_type: fileType,
        processed_at: new Date().toISOString(),
        processing_time_ms: 0
      }
    };

    // Convert R2 object to array buffer for AI processing
    const imageBuffer = await r2Object.arrayBuffer();
    const imageArray = new Uint8Array(imageBuffer);

    // Run NSFW detection first - if NSFW, skip other models to save costs
    if (env.ENABLE_NSFW_DETECTION === 'true') {
      try {
        const nsfwResult = await env.AI.run(env.NSFW_MODEL_NAME, {
          image: imageArray
        }) as NSFWDetectionResult;

        result.nsfw_analysis = nsfwResult;

        // If content is NSFW, mark as rejected and skip other AI models
        if (nsfwResult.is_nsfw || nsfwResult.nsfw_score > 0.7) {
          result.status = 'rejected_nsfw';
          result.rejection_reason = `Content flagged as NSFW (score: ${nsfwResult.nsfw_score})`;
          result.metadata.processing_time_ms = Date.now() - startTime;

          // Store rejection result immediately
          await storeProcessingResults(result, env);
          return result;
        }
      } catch (error) {
        console.error('NSFW detection failed:', error);
        if (env.DEBUG_MODE === 'true') {
          result.error = `NSFW detection failed: ${(error as Error).message}`;
        }
      }
    }

    // Only run other AI models if content is safe
    // Run video game recognition if enabled
    if (env.ENABLE_VIDEOGAME_RECOGNITION === 'true') {
      try {
        const gameResult = await env.AI.run(env.VIDEOGAME_MODEL_NAME, {
          image: imageArray
        }) as VideoGameRecognitionResult;

        result.videogame_analysis = gameResult;
      } catch (error) {
        console.error('Video game recognition failed:', error);
        if (env.DEBUG_MODE === 'true') {
          result.error = result.error ?
            `${result.error}; Game recognition failed: ${(error as Error).message}` :
            `Game recognition failed: ${(error as Error).message}`;
        }
      }
    }

    // Run CLIP tagging for video game adjacent tags
    if (env.CLIP_MODEL_NAME) {
      try {
        const clipResult = await runCLIPTagging(imageArray, env);
        result.clip_tagging = clipResult;
      } catch (error) {
        console.error('CLIP tagging failed:', error);
        if (env.DEBUG_MODE === 'true') {
          result.error = result.error ?
            `${result.error}; CLIP tagging failed: ${(error as Error).message}` :
            `CLIP tagging failed: ${(error as Error).message}`;
        }
      }
    }

    // Calculate processing time
    result.metadata.processing_time_ms = Date.now() - startTime;
    result.status = 'completed';

    // Store results for AWS backend retrieval
    await storeProcessingResults(result, env);

    return result;

  } catch (error) {
    console.error('Image processing failed:', error);
    return {
      mediaId,
      status: 'failed',
      metadata: {
        file_size: fileSize,
        file_type: fileType,
        processed_at: new Date().toISOString(),
        processing_time_ms: Date.now() - startTime
      },
      error: env.DEBUG_MODE === 'true' ? (error as Error).message : 'Processing failed'
    };
  }
}

/**
 * Validate file metadata (for presigned URL generation)
 */
function validateFileMetadata(fileType: string, fileSize: number, env: Env): { valid: boolean; error?: string } {
  // Check file size
  const maxSizeMB = parseInt(env.MAX_FILE_SIZE_MB) || 50;
  const maxSizeBytes = maxSizeMB * 1024 * 1024;

  if (fileSize && fileSize > maxSizeBytes) {
    return {
      valid: false,
      error: `File size exceeds ${maxSizeMB}MB limit`
    };
  }

  // Check file type
  const supportedTypes = env.SUPPORTED_IMAGE_TYPES?.split(',') || [
    'image/jpeg', 'image/png', 'image/webp', 'image/gif'
  ];

  if (!supportedTypes.includes(fileType)) {
    return {
      valid: false,
      error: `Unsupported file type. Supported types: ${supportedTypes.join(', ')}`
    };
  }

  return { valid: true };
}

/**
 * Generate R2 key for uploaded file
 */
function generateR2Key(userId: string, mediaId: string, fileName: string, mediaType: string = 'media'): string {
  const extension = getFileExtension(fileName);

  switch (mediaType) {
    case 'avatar':
      return `avatars/${userId}/${mediaId}.${extension}`;
    case 'reflex':
      return `reflexes/${userId}/${mediaId}.${extension}`;
    case 'video':
      return `videos/${userId}/${mediaId}.${extension}`;
    default:
      return `media/${userId}/${mediaId}.${extension}`;
  }
}

/**
 * Get file extension from filename
 */
function getFileExtension(filename: string): string {
  return filename.split('.').pop()?.toLowerCase() || 'jpg';
}

/**
 * Store processing results for AWS backend retrieval
 */
async function storeProcessingResults(result: ProcessingResult, env: Env): Promise<void> {
  try {
    // Store as JSON in R2 metadata bucket
    const metadataKey = `metadata/${result.mediaId}.json`;

    await env.MEDIA_BUCKET.put(metadataKey, JSON.stringify(result, null, 2), {
      httpMetadata: {
        contentType: 'application/json',
      },
      customMetadata: {
        type: 'processing_result',
        mediaId: result.mediaId,
        status: result.status,
        createdAt: new Date().toISOString(),
      }
    });

    // Optionally send webhook to AWS backend
    if (env.API_BASE_URL && env.WEBHOOK_SECRET) {
      await sendWebhook(result, env);
    }

  } catch (error) {
    console.error('Failed to store processing results:', error);
    // Don't throw here as the main processing succeeded
  }
}

/**
 * Send webhook notification to AWS backend
 */
async function sendWebhook(result: ProcessingResult, env: Env): Promise<void> {
  try {
    const webhookUrl = `${env.API_BASE_URL}/webhooks/cloudflare/media-processed`;

    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Webhook-Secret': env.WEBHOOK_SECRET,
        'User-Agent': 'GameFlex-Cloudflare-Worker/1.0'
      },
      body: JSON.stringify({
        event: 'media.processed',
        data: result,
        timestamp: new Date().toISOString(),
        environment: env.ENVIRONMENT
      })
    });

    if (!response.ok) {
      console.error('Webhook failed:', response.status, await response.text());
    }

  } catch (error) {
    console.error('Webhook error:', error);
    // Don't throw here as it's not critical
  }
}

/**
 * Run CLIP tagging with video game focused prompts
 */
async function runCLIPTagging(imageArray: Uint8Array, env: Env): Promise<CLIPTaggingResult> {
  // Video game specific tags to look for
  const videoGameTags = [
    'video game', 'gaming', 'game screenshot', 'gameplay', 'game interface',
    'game menu', 'game character', 'game world', 'game environment',
    'first person shooter', 'fps', 'rpg', 'mmorpg', 'strategy game',
    'platformer', 'racing game', 'sports game', 'fighting game',
    'minecraft', 'fortnite', 'call of duty', 'league of legends',
    'world of warcraft', 'overwatch', 'apex legends', 'valorant',
    'game ui', 'health bar', 'minimap', 'inventory', 'skill tree',
    'game controller', 'keyboard and mouse', 'gaming setup',
    'esports', 'streaming', 'twitch', 'gaming chair', 'gaming monitor'
  ];

  // General gaming-adjacent tags
  const generalTags = [
    'computer', 'monitor', 'screen', 'digital art', 'fantasy',
    'sci-fi', 'action', 'adventure', 'character', 'weapon',
    'armor', 'magic', 'technology', 'futuristic', 'medieval'
  ];

  const allTags = [...videoGameTags, ...generalTags];

  try {
    // Run CLIP with video game focused prompt
    const clipResult = await env.AI.run(env.CLIP_MODEL_NAME, {
      image: imageArray,
      text: allTags
    }) as any;

    // Process results to separate video game tags from general tags
    const results: CLIPTaggingResult = {
      tags: [],
      confidence_scores: [],
      video_game_tags: [],
      general_tags: []
    };

    // Parse CLIP results (format may vary based on actual model response)
    if (clipResult && clipResult.scores) {
      const tagScores = clipResult.scores.map((score: number, index: number) => ({
        tag: allTags[index],
        score: score
      }));

      // Sort by confidence and filter high-confidence tags
      const highConfidenceTags = tagScores
        .filter((item: any) => item.score > 0.3) // Adjust threshold as needed
        .sort((a: any, b: any) => b.score - a.score)
        .slice(0, 10); // Top 10 tags

      for (const item of highConfidenceTags) {
        results.tags.push(item.tag);
        results.confidence_scores.push(item.score);

        if (videoGameTags.includes(item.tag)) {
          results.video_game_tags.push(item.tag);
        } else {
          results.general_tags.push(item.tag);
        }
      }
    }

    return results;
  } catch (error) {
    console.error('CLIP tagging error:', error);
    throw error;
  }
}

/**
 * Cleanup temporary files (scheduled task)
 */
async function cleanupTempFiles(env: Env): Promise<void> {
  try {
    console.log('Starting cleanup of temporary files');

    // List objects in temp/ folder
    const tempObjects = await env.MEDIA_BUCKET.list({ prefix: 'temp/' });

    const cutoffTime = new Date();
    cutoffTime.setHours(cutoffTime.getHours() - 24); // 24 hours ago

    let deletedCount = 0;

    for (const object of tempObjects.objects) {
      if (object.uploaded && object.uploaded < cutoffTime) {
        await env.MEDIA_BUCKET.delete(object.key);
        deletedCount++;
      }
    }

    console.log(`Cleanup completed: deleted ${deletedCount} temporary files`);

  } catch (error) {
    console.error('Cleanup failed:', error);
  }
}
