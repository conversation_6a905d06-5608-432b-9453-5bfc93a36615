name = "gameflex-media-processor"
main = "workers/image-processor/src/index.ts"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# Default to development environment
[env.development]
name = "gameflex-media-development"
vars = { ENVIRONMENT = "development" }

[env.development.r2_buckets]
MEDIA_BUCKET = { bucket_name = "gameflex-media-development" }

[env.development.ai]
binding = "AI"

# Staging environment
[env.staging]
name = "gameflex-media-staging"
vars = { ENVIRONMENT = "staging" }

[env.staging.r2_buckets]
MEDIA_BUCKET = { bucket_name = "gameflex-media-staging" }

[env.staging.ai]
binding = "AI"

# Production environment
[env.production]
name = "gameflex-media-production"
vars = { ENVIRONMENT = "production" }

[env.production.r2_buckets]
MEDIA_BUCKET = { bucket_name = "gameflex-media-production" }

[env.production.ai]
binding = "AI"

# Global settings
[build]
command = "npm run build"

[build.upload]
format = "modules"

# Development settings
[dev]
port = 8787
local_protocol = "http"

# Observability
[observability]
enabled = true

# Limits and performance
[limits]
cpu_ms = 50000
memory_mb = 128

# Triggers
[[triggers]]
crons = ["0 2 * * *"]  # Daily cleanup at 2 AM UTC
