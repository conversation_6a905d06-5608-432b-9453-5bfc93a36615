# GameFlex Cloudflare Deployment Guide

This guide covers deploying the GameFlex Cloudflare infrastructure for AI-powered image processing.

## Prerequisites

### Required Tools
- **Node.js 18+**: For building and running the deployment scripts
- **Wrangler CLI**: Cloudflare's command-line tool
- **Git**: For version control

### Installation
```bash
# Install Node.js (if not already installed)
# Visit https://nodejs.org/ or use a package manager

# Install Wrangler CLI globally
npm install -g wrangler

# Verify installation
wrangler --version
node --version
```

### Cloudflare Account Setup
1. **Create Cloudflare Account**: Sign up at https://cloudflare.com
2. **Enable Workers**: Go to Workers & Pages in your dashboard
3. **Enable R2**: Go to R2 Object Storage in your dashboard
4. **Get API Token**: 
   - Go to "My Profile" > "API Tokens"
   - Create token with "Custom token" template
   - Permissions needed:
     - Zone:Zone:Read
     - Zone:Zone Settings:Edit
     - Account:Cloudflare Workers:Edit
     - Account:Account Settings:Read
     - Account:R2:Edit

## Environment Configuration

### Required Environment Variables

Add these to your GitLab CI/CD variables or local environment:

```bash
# Cloudflare Configuration
CLOUDFLARE_API_TOKEN=your_api_token_here
CLOUDFLARE_ACCOUNT_ID=your_account_id_here

# For staging environment
CLOUDFLARE_API_TOKEN_STAGING=your_staging_api_token_here

# For production environment  
CLOUDFLARE_API_TOKEN_PROD=your_production_api_token_here

# Optional: Slack notifications
SLACK_WEBHOOK_URL=your_slack_webhook_url_here
```

### GitLab CI/CD Variables Setup

1. Go to your GitLab project
2. Navigate to Settings > CI/CD > Variables
3. Add the following variables:

| Variable | Type | Protected | Masked | Description |
|----------|------|-----------|--------|-------------|
| `CLOUDFLARE_API_TOKEN` | Variable | No | Yes | Development API token |
| `CLOUDFLARE_ACCOUNT_ID` | Variable | No | No | Your Cloudflare account ID |
| `CLOUDFLARE_API_TOKEN_STAGING` | Variable | Yes | Yes | Staging API token |
| `CLOUDFLARE_API_TOKEN_PROD` | Variable | Yes | Yes | Production API token |

## Deployment Methods

### 1. Manual Deployment

From the backend directory:

```bash
# Deploy to development
./deploy-cloudflare.sh development

# Deploy to staging
./deploy-cloudflare.sh staging

# Deploy to production (requires confirmation)
./deploy-cloudflare.sh production

# Dry run (see what would be deployed)
./deploy-cloudflare.sh development --dry-run
```

### 2. GitLab CI/CD Deployment

The deployment is integrated into the main GitLab CI/CD pipeline:

- **Development**: Automatically deploys when changes are pushed to `main` or `develop` branches
- **Staging**: Manual deployment from `main` branch
- **Production**: Manual deployment from tagged releases

### 3. Individual Component Deployment

Deploy specific components:

```bash
cd cloudflare

# Deploy only Workers
./scripts/deploy-workers.sh development

# Setup only R2 buckets
./scripts/setup-r2.sh development gameflex-media-development

# Cleanup environment
./scripts/cleanup.sh development
```

## Deployment Verification

### 1. Health Check
```bash
# Check if the worker is running
curl https://gameflex-media-development.workers.dev/health

# Expected response:
{
  "status": "healthy",
  "environment": "development",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 2. Test Image Upload
```bash
# Test image upload endpoint
curl -X POST https://gameflex-media-development.workers.dev/upload \
  -F "file=@test-image.jpg" \
  -F "mediaId=test-123" \
  -F "userId=user-456"
```

### 3. Verify R2 Bucket
```bash
# List R2 buckets
wrangler r2 bucket list

# Should show: gameflex-media-development, gameflex-media-staging, gameflex-media-production
```

## Monitoring and Logs

### View Worker Logs
```bash
# Tail logs for development
wrangler tail --env development

# Tail logs for production
wrangler tail --env production
```

### Cloudflare Dashboard
1. Go to Workers & Pages in your Cloudflare dashboard
2. Click on your worker (e.g., `gameflex-media-development`)
3. View metrics, logs, and performance data

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   ```bash
   # Re-authenticate with Cloudflare
   wrangler auth login
   ```

2. **Bucket Already Exists**
   ```bash
   # Check existing buckets
   wrangler r2 bucket list
   
   # Delete if needed (CAUTION: This deletes all data)
   wrangler r2 bucket delete gameflex-media-development
   ```

3. **Worker Deployment Failed**
   ```bash
   # Check wrangler.toml configuration
   cat cloudflare/wrangler.toml
   
   # Verify build output
   cd cloudflare && npm run build
   ```

4. **AI Models Not Working**
   - Verify your Cloudflare account has AI features enabled
   - Check model names in environment configuration
   - Ensure sufficient compute limits

### Debug Mode

Enable debug mode for detailed logging:

```bash
# Set debug mode in environment file
echo "DEBUG_MODE=true" >> cloudflare/environments/development.env

# Redeploy
./deploy-cloudflare.sh development
```

## Security Considerations

1. **API Tokens**: Use separate tokens for each environment
2. **CORS**: Configure appropriate origins for each environment
3. **Rate Limiting**: Adjust limits based on expected traffic
4. **File Validation**: Ensure proper file type and size validation

## Performance Optimization

1. **Caching**: Configure appropriate cache headers
2. **Compression**: Enable compression for large files
3. **CDN**: Use Cloudflare's CDN for global distribution
4. **Monitoring**: Set up alerts for performance metrics

## Rollback Procedure

If a deployment fails or causes issues:

```bash
# 1. Check previous deployment
wrangler deployments list --env production

# 2. Rollback to previous version
wrangler rollback --env production

# 3. Verify rollback
curl https://gameflex-media-production.workers.dev/health
```

## Support

For issues with this deployment:

1. Check the logs using `wrangler tail`
2. Review the GitLab CI/CD pipeline logs
3. Verify environment configuration
4. Check Cloudflare dashboard for service status

## Next Steps

After successful deployment:

1. Configure custom domains for R2 buckets
2. Set up monitoring and alerting
3. Test with real image uploads
4. Update AWS backend to use new endpoints
5. Configure CDN caching rules
