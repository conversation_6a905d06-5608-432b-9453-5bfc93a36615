# GameFlex Cloudflare Production Environment Configuration

# Environment
ENVIRONMENT=production
NODE_ENV=production

# Cloudflare Configuration
CLOUDFLARE_ACCOUNT_ID=your_account_id_here
CLOUDFLARE_API_TOKEN=your_production_api_token_here

# R2 Storage
R2_BUCKET_NAME=gameflex-media-production
R2_PUBLIC_URL=https://pub-your-account-id.r2.dev
R2_CUSTOM_DOMAIN=https://media.gameflex.io

# AI Models Configuration
NSFW_MODEL_NAME=@cf/falconsai/nsfw_image_detection
VIDEOGAME_MODEL_NAME=@cf/videogamebunny/videogame_image_recognition

# AWS Integration (for storing results)
AWS_REGION=us-west-2
AWS_DYNAMODB_TABLE=gameflex-media-analysis-production
AWS_SQS_QUEUE=gameflex-media-processing-production

# API Configuration
API_BASE_URL=https://api.gameflex.io
WEBHOOK_SECRET=your_production_webhook_secret_here

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=500
RATE_LIMIT_BURST=100

# Processing Configuration
MAX_FILE_SIZE_MB=200
SUPPORTED_IMAGE_TYPES=image/jpeg,image/png,image/webp
SUPPORTED_VIDEO_TYPES=video/mp4,video/mov

# Logging and Monitoring
LOG_LEVEL=warn
ENABLE_ANALYTICS=true
ENABLE_TRACING=true

# Security
ALLOWED_ORIGINS=https://gameflex.io,https://www.gameflex.io
CORS_MAX_AGE=1800

# Feature Flags
ENABLE_NSFW_DETECTION=true
ENABLE_VIDEOGAME_RECOGNITION=true
ENABLE_THUMBNAIL_GENERATION=true
ENABLE_METADATA_EXTRACTION=true

# Production Specific
DEBUG_MODE=false
MOCK_AI_RESPONSES=false
SKIP_WEBHOOK_VERIFICATION=false
