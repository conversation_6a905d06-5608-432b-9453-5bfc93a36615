# GameFlex Cloudflare Development Environment Configuration

# Environment
ENVIRONMENT=development
NODE_ENV=development

# Cloudflare Configuration
CLOUDFLARE_ACCOUNT_ID=your_account_id_here
CLOUDFLARE_API_TOKEN=your_api_token_here

# R2 Storage
R2_BUCKET_NAME=gameflex-media-development
R2_PUBLIC_URL=https://pub-your-account-id.r2.dev
R2_CUSTOM_DOMAIN=https://media-dev.gameflex.io

# AI Models Configuration
NSFW_MODEL_NAME=@cf/falconsai/nsfw_image_detection
VIDEOGAME_MODEL_NAME=@cf/videogamebunny/videogame_image_recognition

# AWS Integration (for storing results)
AWS_REGION=us-west-2
AWS_DYNAMODB_TABLE=gameflex-media-analysis-development
AWS_SQS_QUEUE=gameflex-media-processing-development

# API Configuration
API_BASE_URL=https://dev.api.gameflex.io
WEBHOOK_SECRET=your_webhook_secret_here

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=20

# Processing Configuration
MAX_FILE_SIZE_MB=50
SUPPORTED_IMAGE_TYPES=image/jpeg,image/png,image/webp,image/gif
SUPPORTED_VIDEO_TYPES=video/mp4,video/mov,video/avi

# Logging and Monitoring
LOG_LEVEL=debug
ENABLE_ANALYTICS=true
ENABLE_TRACING=true

# Security
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080,https://dev.gameflex.io,capacitor://localhost,ionic://localhost
CORS_MAX_AGE=3600

# Feature Flags
ENABLE_NSFW_DETECTION=true
ENABLE_VIDEOGAME_RECOGNITION=true
ENABLE_THUMBNAIL_GENERATION=true
ENABLE_METADATA_EXTRACTION=true

# Development Specific
DEBUG_MODE=true
MOCK_AI_RESPONSES=false
SKIP_WEBHOOK_VERIFICATION=true
