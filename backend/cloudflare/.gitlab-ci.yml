# GameFlex Cloudflare CI/CD Pipeline
# Deploys Cloudflare Workers and R2 infrastructure

# Global variables
variables:
  # Node.js configuration
  NODE_IMAGE: "node:18-alpine"
  NODE_VERSION: "18"
  
  # Cloudflare configuration
  CLOUDFLARE_API_TOKEN: "$CLOUDFLARE_API_TOKEN"
  CLOUDFLARE_ACCOUNT_ID: "$CLOUDFLARE_ACCOUNT_ID"
  
  # Project configuration
  PROJECT_NAME: "gameflex-cloudflare"

# Pipeline stages
stages:
  - validate
  - build
  - test
  - deploy-dev
  - deploy-staging
  - deploy-production

# Global cache configuration
cache:
  key:
    files:
      - package-lock.json
  paths:
    - node_modules/
    - .npm/

# ============================================================================
# VALIDATION STAGE
# ============================================================================

# Validate Cloudflare configuration
validate-config:
  stage: validate
  image: $NODE_IMAGE
  script:
    - echo "Validating Cloudflare configuration..."
    - cd cloudflare
    - npm install -g wrangler
    - wrangler --version
    - |
      if [ -z "$CLOUDFLARE_API_TOKEN" ]; then
        echo "Error: CLOUDFLARE_API_TOKEN is not set"
        exit 1
      fi
    - |
      if [ -z "$CLOUDFLARE_ACCOUNT_ID" ]; then
        echo "Error: CLOUDFLARE_ACCOUNT_ID is not set"
        exit 1
      fi
    - echo "Configuration validation passed"
  rules:
    - if: $CI_COMMIT_BRANCH
      changes:
        - "cloudflare/**/*"
    - if: $CI_MERGE_REQUEST_IID
      changes:
        - "cloudflare/**/*"

# Lint TypeScript code
lint-typescript:
  stage: validate
  image: $NODE_IMAGE
  before_script:
    - cd cloudflare
    - npm install
  script:
    - echo "Linting TypeScript code..."
    - npm run lint
    - npm run type-check
    - echo "TypeScript linting passed"
  rules:
    - if: $CI_COMMIT_BRANCH
      changes:
        - "cloudflare/**/*.ts"
        - "cloudflare/**/*.js"
        - "cloudflare/package.json"
    - if: $CI_MERGE_REQUEST_IID
      changes:
        - "cloudflare/**/*.ts"
        - "cloudflare/**/*.js"
        - "cloudflare/package.json"

# ============================================================================
# BUILD STAGE
# ============================================================================

# Build Workers
build-workers:
  stage: build
  image: $NODE_IMAGE
  before_script:
    - cd cloudflare
    - npm install
  script:
    - echo "Building Cloudflare Workers..."
    - npm run build
    - echo "Workers built successfully"
  artifacts:
    paths:
      - cloudflare/dist/
    expire_in: 1 hour
  rules:
    - if: $CI_COMMIT_BRANCH
      changes:
        - "cloudflare/**/*"
    - if: $CI_MERGE_REQUEST_IID
      changes:
        - "cloudflare/**/*"

# ============================================================================
# TEST STAGE
# ============================================================================

# Test Workers (if tests exist)
test-workers:
  stage: test
  image: $NODE_IMAGE
  dependencies:
    - build-workers
  before_script:
    - cd cloudflare
    - npm install
  script:
    - echo "Running Worker tests..."
    - npm test || echo "No tests found - skipping"
    - echo "Tests completed"
  rules:
    - if: $CI_COMMIT_BRANCH
      changes:
        - "cloudflare/**/*"
    - if: $CI_MERGE_REQUEST_IID
      changes:
        - "cloudflare/**/*"

# ============================================================================
# DEVELOPMENT DEPLOYMENT
# ============================================================================

# Deploy to development
deploy-cloudflare-dev:
  stage: deploy-dev
  image: $NODE_IMAGE
  dependencies:
    - build-workers
  before_script:
    - cd cloudflare
    - npm install -g wrangler
    - echo "$CLOUDFLARE_API_TOKEN" | wrangler auth login --api-token
  script:
    - echo "Deploying to development environment..."
    - ../deploy-cloudflare.sh development --force
    - echo "Development deployment completed"
  environment:
    name: development
    url: https://gameflex-media-development.workers.dev
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      changes:
        - "cloudflare/**/*"
    - if: $CI_COMMIT_BRANCH == "develop"
      changes:
        - "cloudflare/**/*"

# ============================================================================
# STAGING DEPLOYMENT
# ============================================================================

# Deploy to staging
deploy-cloudflare-staging:
  stage: deploy-staging
  image: $NODE_IMAGE
  dependencies:
    - build-workers
  before_script:
    - cd cloudflare
    - npm install -g wrangler
    - echo "$CLOUDFLARE_API_TOKEN_STAGING" | wrangler auth login --api-token
  script:
    - echo "Deploying to staging environment..."
    - ../deploy-cloudflare.sh staging --force
    - echo "Staging deployment completed"
  environment:
    name: staging
    url: https://gameflex-media-staging.workers.dev
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      changes:
        - "cloudflare/**/*"
      when: manual
    - if: $CI_COMMIT_TAG
      when: manual

# ============================================================================
# PRODUCTION DEPLOYMENT
# ============================================================================

# Deploy to production
deploy-cloudflare-production:
  stage: deploy-production
  image: $NODE_IMAGE
  dependencies:
    - build-workers
  before_script:
    - cd cloudflare
    - npm install -g wrangler
    - echo "$CLOUDFLARE_API_TOKEN_PROD" | wrangler auth login --api-token
  script:
    - echo "Deploying to PRODUCTION environment..."
    - echo "This is a PRODUCTION deployment!"
    - ../deploy-cloudflare.sh production --force
    - echo "Production deployment completed"
  environment:
    name: production
    url: https://gameflex-media-production.workers.dev
  rules:
    - if: $CI_COMMIT_TAG
      when: manual
  allow_failure: false

# ============================================================================
# CLEANUP JOBS
# ============================================================================

# Cleanup development environment
cleanup-dev:
  stage: deploy-dev
  image: $NODE_IMAGE
  before_script:
    - cd cloudflare
    - npm install -g wrangler
    - echo "$CLOUDFLARE_API_TOKEN" | wrangler auth login --api-token
  script:
    - echo "Cleaning up development environment..."
    - ./scripts/cleanup.sh development true
    - echo "Development cleanup completed"
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  allow_failure: true

# ============================================================================
# NOTIFICATION JOBS
# ============================================================================

# Notify deployment success
notify-success:
  stage: deploy-production
  image: alpine:latest
  before_script:
    - apk add --no-cache curl
  script:
    - echo "Sending deployment success notification..."
    - |
      if [ -n "$SLACK_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
          --data "{\"text\":\"✅ GameFlex Cloudflare deployment successful for $CI_ENVIRONMENT_NAME\"}" \
          "$SLACK_WEBHOOK_URL"
      fi
    - echo "Notification sent"
  rules:
    - if: $CI_COMMIT_TAG
  allow_failure: true
