# GameFlex Cloudflare API Usage Guide

This guide shows how to use the new cost-optimized Cloudflare API for image uploads and AI processing.

## API Flow Overview

The new API separates upload and processing to minimize costs:

1. **Get Upload URL**: Request a presigned URL for direct R2 upload
2. **Upload File**: Upload directly to R2 using the presigned URL
3. **Process Image**: Trigger AI processing after upload is complete

## API Endpoints

### 1. Get Upload URL

**Endpoint**: `POST /upload-url`

**Purpose**: Generate a presigned URL for direct R2 upload

**Request Body**:
```json
{
  "mediaId": "unique-media-id",
  "userId": "user-123",
  "fileName": "image.jpg",
  "fileType": "image/jpeg",
  "fileSize": 1024000,
  "mediaType": "image"
}
```

**Response**:
```json
{
  "success": true,
  "mediaId": "unique-media-id",
  "uploadUrl": "presigned-r2-upload-url",
  "r2Key": "media/user-123/unique-media-id.jpg",
  "publicUrl": "https://gameflex-media-dev.r2.dev/media/user-123/unique-media-id.jpg",
  "expiresIn": 600,
  "metadata": {
    "mediaId": "unique-media-id",
    "userId": "user-123",
    "fileName": "image.jpg",
    "fileType": "image/jpeg",
    "fileSize": 1024000,
    "mediaType": "image",
    "r2Key": "media/user-123/unique-media-id.jpg",
    "uploadedAt": "2024-01-01T00:00:00.000Z",
    "status": "pending_upload"
  }
}
```

### 2. Process Image

**Endpoint**: `POST /process`

**Purpose**: Trigger AI processing for an uploaded image

**Request Body**:
```json
{
  "mediaId": "unique-media-id",
  "r2Key": "media/user-123/unique-media-id.jpg"
}
```

**Response**:
```json
{
  "mediaId": "unique-media-id",
  "status": "completed",
  "nsfw_analysis": {
    "nsfw_score": 0.1,
    "is_nsfw": false,
    "categories": {
      "safe": 0.9,
      "suggestive": 0.08,
      "explicit": 0.02
    }
  },
  "videogame_analysis": {
    "game_detected": true,
    "game_title": "Minecraft",
    "confidence": 0.95,
    "description": "A screenshot from Minecraft showing a blocky landscape",
    "tags": ["minecraft", "sandbox", "building", "blocks"],
    "genre": "Sandbox"
  },
  "metadata": {
    "file_size": 1024000,
    "file_type": "image/jpeg",
    "processed_at": "2024-01-01T00:00:00.000Z",
    "processing_time_ms": 1500
  }
}
```

### 3. Get Media

**Endpoint**: `GET /media/{r2Key}`

**Purpose**: Retrieve uploaded media files

**Example**: `GET /media/user-123/unique-media-id.jpg`

### 4. Health Check

**Endpoint**: `GET /health`

**Response**:
```json
{
  "status": "healthy",
  "environment": "development",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Usage Examples

### JavaScript/TypeScript Example

```typescript
class GameFlexMediaService {
  private baseUrl: string;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }

  async uploadImage(file: File, mediaId: string, userId: string): Promise<string> {
    // Step 1: Get upload URL
    const uploadUrlResponse = await fetch(`${this.baseUrl}/upload-url`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        mediaId,
        userId,
        fileName: file.name,
        fileType: file.type,
        fileSize: file.size,
        mediaType: 'image'
      })
    });

    const uploadData = await uploadUrlResponse.json();
    
    if (!uploadData.success) {
      throw new Error(`Failed to get upload URL: ${uploadData.error}`);
    }

    // Step 2: Upload directly to R2
    const uploadResponse = await fetch(uploadData.uploadUrl, {
      method: 'PUT',
      body: file,
      headers: {
        'Content-Type': file.type,
      }
    });

    if (!uploadResponse.ok) {
      throw new Error('Failed to upload file to R2');
    }

    // Step 3: Trigger AI processing
    const processResponse = await fetch(`${this.baseUrl}/process`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        mediaId,
        r2Key: uploadData.r2Key
      })
    });

    const processResult = await processResponse.json();
    
    if (processResult.status === 'failed') {
      throw new Error(`Processing failed: ${processResult.error}`);
    }

    return uploadData.publicUrl;
  }

  async processExistingImage(mediaId: string): Promise<any> {
    const response = await fetch(`${this.baseUrl}/process`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ mediaId })
    });

    return await response.json();
  }
}

// Usage
const mediaService = new GameFlexMediaService('https://gameflex-media-development.workers.dev');

// Upload and process new image
const fileInput = document.getElementById('file-input') as HTMLInputElement;
const file = fileInput.files[0];
const mediaId = generateUniqueId();
const userId = getCurrentUserId();

try {
  const publicUrl = await mediaService.uploadImage(file, mediaId, userId);
  console.log('Image uploaded and processed:', publicUrl);
} catch (error) {
  console.error('Upload failed:', error);
}
```

### cURL Examples

```bash
# 1. Get upload URL
curl -X POST https://gameflex-media-development.workers.dev/upload-url \
  -H "Content-Type: application/json" \
  -d '{
    "mediaId": "test-123",
    "userId": "user-456",
    "fileName": "test.jpg",
    "fileType": "image/jpeg",
    "fileSize": 1024000
  }'

# 2. Upload file (using presigned URL from step 1)
curl -X PUT "PRESIGNED_URL_FROM_STEP_1" \
  -H "Content-Type: image/jpeg" \
  --data-binary @test.jpg

# 3. Process image
curl -X POST https://gameflex-media-development.workers.dev/process \
  -H "Content-Type: application/json" \
  -d '{
    "mediaId": "test-123",
    "r2Key": "media/user-456/test-123.jpg"
  }'

# 4. Get processed image
curl https://gameflex-media-development.workers.dev/media/user-456/test-123.jpg
```

## Error Handling

### Common Error Responses

```json
{
  "error": "Missing required fields: mediaId, userId, fileName, fileType",
  "details": "Additional error details in debug mode"
}
```

```json
{
  "error": "File size exceeds 50MB limit",
  "details": "Current file size: 75MB"
}
```

```json
{
  "error": "Unsupported file type: image/bmp",
  "details": "Supported types: image/jpeg, image/png, image/webp, image/gif"
}
```

## Integration with AWS Backend

The processed results are automatically stored in R2 and can be retrieved by your AWS backend:

1. **Webhook Notification**: Worker sends webhook to AWS when processing completes
2. **Metadata Storage**: Results stored in `metadata/processed/{mediaId}.json`
3. **Direct Retrieval**: AWS can fetch results directly from R2

## Cost Optimization Benefits

- **No Worker Upload Costs**: Files upload directly to R2
- **Minimal Processing Time**: Workers only run for AI processing
- **Efficient Storage**: Automatic cleanup of temporary files
- **Reduced Bandwidth**: No double transfer through Workers

## Rate Limits

- **Upload URLs**: 100 requests per minute per IP
- **Processing**: 50 requests per minute per IP
- **Media Retrieval**: 1000 requests per minute per IP

## Security Features

- **Presigned URLs**: Expire after 10 minutes
- **File Validation**: Type and size checking
- **CORS Protection**: Environment-specific origins
- **Rate Limiting**: Prevents abuse
