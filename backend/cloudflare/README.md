# GameFlex Cloudflare Infrastructure

This directory contains the Cloudflare infrastructure for GameFlex, including:

- **R2 Storage**: Object storage for images and videos across all environments
- **Workers**: AI-powered image processing with NSFW detection and video game recognition
- **Deployment Scripts**: Automated deployment across development, staging, and production

## Directory Structure

```
backend/cloudflare/
├── README.md                    # This file
├── wrangler.toml               # Wrangler configuration
├── package.json                # Dependencies and scripts
├── workers/                    # Cloudflare Workers
│   ├── image-processor/        # AI image processing worker
│   └── shared/                 # Shared utilities
├── r2/                         # R2 bucket configurations
│   ├── buckets.json           # Bucket definitions
│   └── cors.json              # CORS configuration
├── environments/              # Environment-specific configs
│   ├── development.env
│   ├── staging.env
│   └── production.env
└── scripts/                   # Utility scripts
    ├── setup-r2.sh
    ├── deploy-workers.sh
    └── cleanup.sh
```

## Environment Naming Convention

All resources follow the pattern: `gameflex-media-{environment}`

- **Development**: `gameflex-media-development`
- **Staging**: `gameflex-media-staging`
- **Production**: `gameflex-media-production`

## Quick Start

From the backend root directory:

1. Deploy Cloudflare infrastructure:
   ```bash
   ./deploy-cloudflare.sh development
   ```

2. Or use the individual commands:
   ```bash
   cd cloudflare
   npm install
   npm run deploy:dev
   ```

## Features

### R2 Storage
- Separate buckets for each environment
- CORS configuration for web uploads
- Public access for media serving
- Lifecycle policies for cost optimization

### AI Image Processing
- **NSFW Detection**: Using Falconsai/nsfw_image_detection model
- **Video Game Recognition**: Using VideoGameBunny for game-specific tagging
- **AWS Integration**: Results stored for AWS backend retrieval

### Security
- Environment-specific API keys
- Secure token handling
- Rate limiting and abuse protection

## Integration with AWS Backend

The Cloudflare Workers process images and store analysis results that can be retrieved by the AWS backend:

1. **Image Upload**: User uploads image to R2 via presigned URL
2. **AI Processing**: Worker automatically processes with NSFW detection and video game recognition
3. **Results Storage**: Analysis results stored in R2 metadata folder and optionally sent via webhook
4. **AWS Retrieval**: AWS backend retrieves processed data for further use

### Data Flow (Cost-Optimized)
```
1. Request Upload URL → Worker generates presigned URL
2. Direct Upload → User uploads directly to R2 (no Worker costs)
3. Trigger Processing → AWS/Client calls Worker to process
4. AI Analysis → Worker processes with AI models
5. Results Storage → Results stored in R2 + webhook to AWS
```

### Cost Benefits
- **No Upload Streaming**: Files go directly to R2, avoiding Worker bandwidth costs
- **On-Demand Processing**: AI processing only runs when explicitly triggered
- **Minimal Worker Time**: Workers only run for URL generation and AI processing
- **Efficient Storage**: Direct R2 uploads with automatic cleanup

## AI Models

### NSFW Detection (Falconsai/nsfw_image_detection)
- **Purpose**: Detect inappropriate content in images
- **Output**: NSFW score, categories (safe/suggestive/explicit), confidence level
- **Use Case**: Content moderation for user-generated images
- **Processing**: **Runs first** - if NSFW detected, other models are skipped to save costs

### Video Game Recognition (VideoGameBunny)
- **Purpose**: Identify video game content and generate descriptions
- **Output**: Game title, description, tags, genre, confidence level
- **Use Case**: Automatic tagging and categorization of gaming content
- **Processing**: Only runs if content passes NSFW check

### CLIP Tagging (CLIP-ViT-L-336px)
- **Purpose**: Generate video game adjacent tags using CLIP model
- **Output**: Categorized tags (video game specific vs general), confidence scores
- **Use Case**: Enhanced tagging for gaming content discovery
- **Processing**: Only runs if content passes NSFW check

## AI Processing Flow

```
1. NSFW Detection (Always First)
   ├─ If NSFW (score > 0.7) → REJECT & Stop Processing
   └─ If Safe → Continue to other models

2. Video Game Recognition (If Safe)
   └─ Detect game titles, genres, descriptions

3. CLIP Tagging (If Safe)
   └─ Generate video game adjacent tags
```

### Cost Optimization
- **Early NSFW Termination**: Saves ~70% processing costs on inappropriate content
- **Conditional Processing**: Only runs expensive models on safe content
- **Smart Thresholds**: Configurable NSFW sensitivity (default: 0.7)

## Deployment

### Quick Start
```bash
# From backend root directory
./deploy-cloudflare.sh development

# With options
./deploy-cloudflare.sh staging --force --verbose
./deploy-cloudflare.sh production --dry-run
```

### Environment-Specific Deployment
```bash
# Development (auto-deploys on code changes)
./deploy-cloudflare.sh development

# Staging (manual deployment)
./deploy-cloudflare.sh staging

# Production (manual, requires confirmation)
./deploy-cloudflare.sh production
```

### Component-Specific Deployment
```bash
cd cloudflare

# Deploy only Workers
./scripts/deploy-workers.sh development

# Setup only R2 buckets
./scripts/setup-r2.sh development gameflex-media-development

# Cleanup environment
./scripts/cleanup.sh development
```

## Configuration

### Environment Files
- `environments/development.env` - Development configuration
- `environments/staging.env` - Staging configuration
- `environments/production.env` - Production configuration
- `.env.example` - Template for creating new environment files

### Key Configuration Options
- **File Limits**: Max file size, supported types
- **AI Features**: Enable/disable NSFW detection and game recognition
- **Rate Limiting**: Requests per minute, burst limits
- **Security**: CORS origins, webhook secrets
- **Monitoring**: Logging levels, analytics

## Monitoring and Debugging

### View Logs
```bash
# Real-time logs
wrangler tail --env development

# Specific worker logs
wrangler tail gameflex-media-development
```

### Health Checks
```bash
# Check worker health
curl https://gameflex-media-development.workers.dev/health

# Test upload URL generation
curl -X POST https://gameflex-media-development.workers.dev/upload-url \
  -H "Content-Type: application/json" \
  -d '{"mediaId":"test-123","userId":"user-456","fileName":"test.jpg","fileType":"image/jpeg"}'

# Test image processing (after upload)
curl -X POST https://gameflex-media-development.workers.dev/process \
  -H "Content-Type: application/json" \
  -d '{"mediaId":"test-123","r2Key":"media/user-456/test-123.jpg"}'
```

### Cloudflare Dashboard
- Workers & Pages: View worker metrics and logs
- R2 Object Storage: Monitor storage usage and costs
- Analytics: Track request patterns and performance

## Security Features

### File Validation
- File type checking (MIME type validation)
- File size limits (configurable per environment)
- Malicious file detection

### Access Control
- CORS configuration for web uploads
- Rate limiting to prevent abuse
- Webhook signature verification

### Data Protection
- Secure token handling
- Environment-specific secrets
- Encrypted data transmission

## Cost Optimization

### R2 Storage
- Lifecycle policies for automatic cleanup
- Separate buckets per environment
- Efficient folder structure

### Workers
- Optimized code for fast execution
- Minimal memory usage
- Efficient AI model usage

### Monitoring
- Track usage patterns
- Set up billing alerts
- Optimize based on metrics

## Troubleshooting

See `DEPLOYMENT.md` for detailed troubleshooting guide including:
- Common deployment issues
- Authentication problems
- Performance optimization
- Rollback procedures
