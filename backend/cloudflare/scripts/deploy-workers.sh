#!/bin/bash

# GameFlex Workers Deployment Script
# Deploys Cloudflare Workers for specific environments

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_status() {
    echo -e "${BLUE}[WORKERS]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[WORKERS SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WORKERS WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[WORKERS ERROR]${NC} $1"
}

# Script parameters
ENVIRONMENT="$1"
DRY_RUN="${2:-false}"

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CLOUDFLARE_DIR="$(dirname "$SCRIPT_DIR")"

print_status "Deploying Workers for environment: $ENVIRONMENT"

# Validate environment
validate_environment() {
    case $ENVIRONMENT in
        development|staging|production)
            print_status "Environment validated: $ENVIRONMENT"
            ;;
        *)
            print_error "Invalid environment: $ENVIRONMENT"
            print_error "Valid environments: development, staging, production"
            exit 1
            ;;
    esac
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."

    # Check if wrangler is installed
    if ! command -v wrangler &> /dev/null; then
        print_error "Wrangler CLI is not installed"
        print_status "Install with: npm install -g wrangler"
        exit 1
    fi

    # Check if authenticated
    if ! wrangler whoami &> /dev/null; then
        print_error "Not authenticated with Cloudflare"
        print_status "Run: wrangler auth login"
        exit 1
    fi

    # Check if in correct directory
    if [[ ! -f "$CLOUDFLARE_DIR/wrangler.toml" ]]; then
        print_error "wrangler.toml not found in $CLOUDFLARE_DIR"
        exit 1
    fi

    print_success "Prerequisites check passed"
}

# Load environment variables
load_environment() {
    local env_file="$CLOUDFLARE_DIR/environments/${ENVIRONMENT}.env"
    
    if [[ -f "$env_file" ]]; then
        print_status "Loading environment variables from $env_file"
        
        # Export environment variables for wrangler
        set -a
        source "$env_file"
        set +a
        
        print_success "Environment variables loaded"
    else
        print_warning "Environment file not found: $env_file"
        print_warning "Using default configuration"
    fi
}

# Build the worker
build_worker() {
    print_status "Building Worker for $ENVIRONMENT..."
    
    cd "$CLOUDFLARE_DIR"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_status "[DRY RUN] Would build Worker"
        return
    fi
    
    # Install dependencies if needed
    if [[ ! -d "node_modules" ]]; then
        print_status "Installing dependencies..."
        npm install
    fi
    
    # Build the worker
    if npm run build; then
        print_success "Worker built successfully"
    else
        print_error "Worker build failed"
        exit 1
    fi
}

# Deploy the worker
deploy_worker() {
    print_status "Deploying Worker to $ENVIRONMENT..."
    
    cd "$CLOUDFLARE_DIR"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_status "[DRY RUN] Would deploy Worker to $ENVIRONMENT"
        return
    fi
    
    # Deploy based on environment
    case $ENVIRONMENT in
        development)
            if wrangler deploy --env development; then
                print_success "Worker deployed to development"
            else
                print_error "Development deployment failed"
                exit 1
            fi
            ;;
        staging)
            if wrangler deploy --env staging; then
                print_success "Worker deployed to staging"
            else
                print_error "Staging deployment failed"
                exit 1
            fi
            ;;
        production)
            print_warning "Deploying to PRODUCTION environment"
            read -p "Are you sure you want to deploy to production? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                if wrangler deploy --env production; then
                    print_success "Worker deployed to production"
                else
                    print_error "Production deployment failed"
                    exit 1
                fi
            else
                print_status "Production deployment cancelled"
                exit 0
            fi
            ;;
    esac
}

# Verify deployment
verify_deployment() {
    print_status "Verifying deployment..."
    
    local worker_name="gameflex-media-$ENVIRONMENT"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_status "[DRY RUN] Would verify deployment"
        return
    fi
    
    # Get worker info
    if wrangler list | grep -q "$worker_name"; then
        print_success "Worker verification passed: $worker_name"
        
        # Test health endpoint
        local worker_url="https://${worker_name}.workers.dev"
        print_status "Testing health endpoint: $worker_url/health"
        
        if curl -s -f "$worker_url/health" > /dev/null; then
            print_success "Health check passed"
        else
            print_warning "Health check failed - worker may still be starting up"
        fi
        
    else
        print_error "Worker verification failed: $worker_name not found"
        exit 1
    fi
}

# Show deployment info
show_deployment_info() {
    print_success "Worker deployment completed!"
    echo
    print_status "Deployment Details:"
    echo "  Environment: $ENVIRONMENT"
    echo "  Worker Name: gameflex-media-$ENVIRONMENT"
    echo "  Worker URL: https://gameflex-media-$ENVIRONMENT.workers.dev"
    echo "  Health Check: https://gameflex-media-$ENVIRONMENT.workers.dev/health"
    echo
    print_status "Available Endpoints:"
    echo "  POST /upload     - Upload and process images"
    echo "  POST /process    - Process existing images"
    echo "  GET  /media/*    - Retrieve media files"
    echo "  GET  /health     - Health check"
    echo
    print_status "Next Steps:"
    echo "  1. Test the endpoints with sample images"
    echo "  2. Configure custom domain if needed"
    echo "  3. Update AWS backend to use new endpoints"
    echo "  4. Monitor logs: wrangler tail --env $ENVIRONMENT"
}

# Main deployment function
main() {
    print_status "Starting Worker deployment for $ENVIRONMENT"
    
    # Validate inputs
    if [[ -z "$ENVIRONMENT" ]]; then
        print_error "Usage: $0 <environment> [dry_run]"
        exit 1
    fi
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_warning "DRY RUN MODE - No actual changes will be made"
    fi
    
    # Execute deployment steps
    validate_environment
    check_prerequisites
    load_environment
    build_worker
    deploy_worker
    verify_deployment
    show_deployment_info
    
    print_success "Worker deployment completed successfully!"
}

# Run main function
main
