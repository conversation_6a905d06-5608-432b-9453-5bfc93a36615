#!/bin/bash

# GameFlex R2 Bucket Setup Script
# Sets up R2 buckets with proper configuration for each environment

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_status() {
    echo -e "${BLUE}[R2 SETUP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[R2 SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[R2 WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[R2 ERROR]${NC} $1"
}

# Script parameters
ENVIRONMENT="$1"
BUCKET_NAME="$2"

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
R2_CONFIG_DIR="$(dirname "$SCRIPT_DIR")/r2"

print_status "Setting up R2 bucket: $BUCKET_NAME for environment: $ENVIRONMENT"

# Check if bucket exists
bucket_exists() {
    wrangler r2 bucket list | grep -q "^$BUCKET_NAME$"
}

# Create bucket if it doesn't exist
create_bucket() {
    if bucket_exists; then
        print_warning "Bucket $BUCKET_NAME already exists"
        return 0
    fi
    
    print_status "Creating R2 bucket: $BUCKET_NAME"
    if wrangler r2 bucket create "$BUCKET_NAME"; then
        print_success "Created bucket: $BUCKET_NAME"
    else
        print_error "Failed to create bucket: $BUCKET_NAME"
        return 1
    fi
}

# Configure CORS for the bucket
configure_cors() {
    print_status "Configuring CORS for bucket: $BUCKET_NAME"
    
    # Create temporary CORS configuration file
    local cors_file="/tmp/gameflex-cors-${ENVIRONMENT}.json"
    
    # Generate CORS configuration based on environment
    case $ENVIRONMENT in
        development)
            cat > "$cors_file" << 'EOF'
[
  {
    "AllowedOrigins": [
      "http://localhost:3000",
      "http://localhost:8080",
      "https://dev.gameflex.io",
      "capacitor://localhost",
      "ionic://localhost"
    ],
    "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD", "OPTIONS"],
    "AllowedHeaders": ["*"],
    "ExposedHeaders": ["ETag", "Content-Length", "Content-Type"],
    "MaxAgeSeconds": 3600
  }
]
EOF
            ;;
        staging)
            cat > "$cors_file" << 'EOF'
[
  {
    "AllowedOrigins": [
      "https://staging.gameflex.io",
      "https://dev.gameflex.io"
    ],
    "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD", "OPTIONS"],
    "AllowedHeaders": ["*"],
    "ExposedHeaders": ["ETag", "Content-Length", "Content-Type"],
    "MaxAgeSeconds": 3600
  }
]
EOF
            ;;
        production)
            cat > "$cors_file" << 'EOF'
[
  {
    "AllowedOrigins": [
      "https://gameflex.io",
      "https://www.gameflex.io"
    ],
    "AllowedMethods": ["GET", "PUT", "POST", "HEAD"],
    "AllowedHeaders": ["Content-Type", "Content-Length", "Authorization"],
    "ExposedHeaders": ["ETag", "Content-Length"],
    "MaxAgeSeconds": 1800
  }
]
EOF
            ;;
    esac
    
    # Apply CORS configuration
    if wrangler r2 bucket cors put "$BUCKET_NAME" --file "$cors_file"; then
        print_success "CORS configured for bucket: $BUCKET_NAME"
    else
        print_error "Failed to configure CORS for bucket: $BUCKET_NAME"
        return 1
    fi
    
    # Clean up temporary file
    rm -f "$cors_file"
}

# Set up bucket lifecycle rules
configure_lifecycle() {
    print_status "Configuring lifecycle rules for bucket: $BUCKET_NAME"
    
    # Note: Lifecycle rules might need to be configured via Cloudflare dashboard
    # or API as wrangler might not support all lifecycle operations yet
    print_warning "Lifecycle rules may need to be configured manually in Cloudflare dashboard"
    print_status "Recommended lifecycle rules for $ENVIRONMENT:"
    
    case $ENVIRONMENT in
        development)
            echo "  - Delete temp/ files after 1 day"
            echo "  - Delete processing/ files after 7 days"
            ;;
        staging)
            echo "  - Delete temp/ files after 3 days"
            echo "  - Delete processing/ files after 14 days"
            ;;
        production)
            echo "  - Delete temp/ files after 7 days"
            echo "  - Delete processing/ files after 30 days"
            echo "  - Archive media/ files to Glacier after 365 days"
            ;;
    esac
}

# Create initial folder structure
create_folder_structure() {
    print_status "Creating initial folder structure in bucket: $BUCKET_NAME"
    
    # Create placeholder files to establish folder structure
    local folders=("media" "avatars" "reflexes" "temp" "processing" "thumbnails" "metadata")
    
    for folder in "${folders[@]}"; do
        local placeholder_content="# GameFlex $folder directory\nCreated: $(date)\nEnvironment: $ENVIRONMENT"
        
        # Create a small placeholder file to establish the folder
        echo -e "$placeholder_content" | wrangler r2 object put "$BUCKET_NAME/$folder/.gitkeep" --file=-
        
        if [[ $? -eq 0 ]]; then
            print_status "Created folder: $folder/"
        else
            print_warning "Failed to create folder: $folder/"
        fi
    done
    
    print_success "Folder structure created"
}

# Verify bucket configuration
verify_bucket() {
    print_status "Verifying bucket configuration: $BUCKET_NAME"
    
    # Check if bucket is accessible
    if wrangler r2 bucket list | grep -q "^$BUCKET_NAME$"; then
        print_success "Bucket verification passed: $BUCKET_NAME"
        
        # Show bucket info
        print_status "Bucket details:"
        echo "  Name: $BUCKET_NAME"
        echo "  Environment: $ENVIRONMENT"
        echo "  Public URL: https://pub-<account-id>.r2.dev (configure custom domain)"
        
        return 0
    else
        print_error "Bucket verification failed: $BUCKET_NAME"
        return 1
    fi
}

# Main setup function
main() {
    print_status "Starting R2 setup for $ENVIRONMENT environment"
    
    # Validate inputs
    if [[ -z "$ENVIRONMENT" || -z "$BUCKET_NAME" ]]; then
        print_error "Usage: $0 <environment> <bucket_name>"
        exit 1
    fi
    
    # Validate environment
    case $ENVIRONMENT in
        development|staging|production)
            ;;
        *)
            print_error "Invalid environment: $ENVIRONMENT"
            print_error "Valid environments: development, staging, production"
            exit 1
            ;;
    esac
    
    # Execute setup steps
    create_bucket
    configure_cors
    configure_lifecycle
    create_folder_structure
    verify_bucket
    
    print_success "R2 setup completed for $ENVIRONMENT environment"
    print_status "Bucket: $BUCKET_NAME is ready for use"
}

# Run main function
main
