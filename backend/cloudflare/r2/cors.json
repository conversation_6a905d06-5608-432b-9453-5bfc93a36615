{"cors_rules": [{"id": "gameflex_web_uploads", "allowed_origins": ["https://dev.gameflex.io", "https://staging.gameflex.io", "https://gameflex.io", "http://localhost:3000", "http://localhost:8080", "capacitor://localhost", "ionic://localhost"], "allowed_methods": ["GET", "PUT", "POST", "DELETE", "HEAD", "OPTIONS"], "allowed_headers": ["*"], "exposed_headers": ["ETag", "Content-Length", "Content-Type", "Last-Modified", "x-amz-request-id", "x-amz-id-2"], "max_age_seconds": 3600}, {"id": "gameflex_mobile_uploads", "allowed_origins": ["*"], "allowed_methods": ["GET", "PUT", "POST", "HEAD"], "allowed_headers": ["Content-Type", "Content-Length", "Authorization", "x-amz-date", "x-amz-content-sha256"], "exposed_headers": ["ETag", "Content-Length"], "max_age_seconds": 1800}], "public_access_policy": {"read_access": true, "write_access": false, "list_access": false}, "security_headers": {"content_security_policy": "default-src 'self'; img-src 'self' data: https:; media-src 'self' https:;", "x_content_type_options": "nosniff", "x_frame_options": "DENY", "referrer_policy": "strict-origin-when-cross-origin"}}