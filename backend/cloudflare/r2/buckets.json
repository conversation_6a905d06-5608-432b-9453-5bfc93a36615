{"buckets": {"development": {"name": "gameflex-media-development", "description": "GameFlex media storage for development environment", "public_access": true, "cors_enabled": true, "lifecycle_rules": [{"id": "cleanup_temp_files", "status": "Enabled", "filter": {"prefix": "temp/"}, "expiration": {"days": 1}}, {"id": "cleanup_processing_files", "status": "Enabled", "filter": {"prefix": "processing/"}, "expiration": {"days": 7}}]}, "staging": {"name": "gameflex-media-staging", "description": "GameFlex media storage for staging environment", "public_access": true, "cors_enabled": true, "lifecycle_rules": [{"id": "cleanup_temp_files", "status": "Enabled", "filter": {"prefix": "temp/"}, "expiration": {"days": 3}}, {"id": "cleanup_processing_files", "status": "Enabled", "filter": {"prefix": "processing/"}, "expiration": {"days": 14}}]}, "production": {"name": "gameflex-media-production", "description": "GameFlex media storage for production environment", "public_access": true, "cors_enabled": true, "lifecycle_rules": [{"id": "cleanup_temp_files", "status": "Enabled", "filter": {"prefix": "temp/"}, "expiration": {"days": 7}}, {"id": "cleanup_processing_files", "status": "Enabled", "filter": {"prefix": "processing/"}, "expiration": {"days": 30}}, {"id": "archive_old_media", "status": "Enabled", "filter": {"prefix": "media/"}, "transition": {"days": 365, "storage_class": "GLACIER"}}]}}, "folder_structure": {"media/": "User uploaded images and videos", "avatars/": "User profile avatars", "reflexes/": "Reflex images", "temp/": "Temporary files during processing", "processing/": "Files being processed by AI", "thumbnails/": "Generated thumbnails", "metadata/": "AI analysis results and metadata"}}