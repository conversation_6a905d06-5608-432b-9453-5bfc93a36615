# GameFlex Cloudflare AI Integration

This document describes the complete integration between the GameFlex AWS backend and Cloudflare AI processing for media uploads.

## Architecture Overview

```
Frontend → AWS API Gateway → Lambda → Cloudflare Worker → AI Models → Webhook → AWS Backend
```

## Complete Workflow

### 1. Media Upload Request
**Endpoint**: `POST /media/upload`

**Frontend Request**:
```json
{
  "fileName": "gaming-screenshot.jpg",
  "fileType": "image/jpeg",
  "fileSize": 1024000,
  "mediaType": "image",
  "postId": "optional-post-id"
}
```

**AWS Backend**:
- Creates media record in DynamoDB with status `pending_upload`
- Calls Cloudflare Worker `/upload-url` endpoint
- Returns presigned R2 upload URL to frontend

**Response**:
```json
{
  "mediaId": "unique-media-id",
  "uploadUrl": "https://presigned-r2-url",
  "media": {
    "id": "unique-media-id",
    "status": "pending_upload",
    "url": "https://public-r2-url"
  },
  "instructions": {
    "step1": "Upload your file to the provided uploadUrl using PUT method",
    "step2": "File will be automatically processed with AI after upload",
    "step3": "Check media status or wait for webhook notification"
  }
}
```

### 2. Direct R2 Upload
**Frontend Action**:
- Uploads file directly to R2 using presigned URL
- No AWS Lambda costs during upload
- Fast, direct upload to Cloudflare R2

### 3. Upload Completion
**Endpoint**: `PUT /media/{id}`

**Frontend Request**:
```json
{
  "status": "uploaded"
}
```

**AWS Backend**:
- Updates media status to `uploaded`
- Media is now ready for AI processing

### 4. AI Processing Trigger
**Endpoint**: `POST /media/{id}/process`

**AWS Backend**:
- Updates media status to `processing`
- Calls Cloudflare Worker `/process` endpoint
- Cloudflare Worker begins AI analysis

**AI Processing Flow**:
```
1. NSFW Detection (Falconsai/nsfw_image_detection)
   ├─ If NSFW (score > 0.7) → REJECT & Stop
   └─ If Safe → Continue

2. Video Game Recognition (VideoGameBunny)
   └─ Detect game titles, genres, descriptions

3. CLIP Tagging (CLIP-ViT-L-336px)
   └─ Generate video game adjacent tags
```

### 5. AI Processing Completion
**Webhook**: `POST /webhooks/cloudflare/media-processed`

**Cloudflare Worker** sends webhook to AWS with results:

**Safe Content Webhook**:
```json
{
  "event": "media.processed",
  "data": {
    "mediaId": "unique-media-id",
    "status": "completed",
    "nsfw_analysis": {
      "nsfw_score": 0.1,
      "is_nsfw": false,
      "categories": { "safe": 0.9, "suggestive": 0.08, "explicit": 0.02 }
    },
    "videogame_analysis": {
      "game_detected": true,
      "game_title": "Minecraft",
      "confidence": 0.95,
      "description": "A screenshot from Minecraft",
      "tags": ["minecraft", "sandbox", "building"],
      "genre": "Sandbox"
    },
    "clip_tagging": {
      "tags": ["video game", "minecraft", "gaming", "blocks"],
      "confidence_scores": [0.95, 0.92, 0.88, 0.85],
      "video_game_tags": ["video game", "minecraft", "gaming"],
      "general_tags": ["blocks"]
    },
    "metadata": {
      "file_size": 1024000,
      "file_type": "image/jpeg",
      "processed_at": "2024-01-01T00:00:00.000Z",
      "processing_time_ms": 1500
    }
  }
}
```

**NSFW Rejection Webhook**:
```json
{
  "event": "media.processed",
  "data": {
    "mediaId": "unique-media-id",
    "status": "rejected_nsfw",
    "nsfw_analysis": {
      "nsfw_score": 0.85,
      "is_nsfw": true,
      "categories": { "safe": 0.1, "suggestive": 0.25, "explicit": 0.65 }
    },
    "rejection_reason": "Content flagged as NSFW (score: 0.85)",
    "metadata": {
      "processing_time_ms": 500
    }
  }
}
```

### 6. AWS Backend Processing
**Webhook Handler**:
- Updates media record with AI results
- If associated with a post, updates post status:
  - `rejected_nsfw` → Post status: `rejected`
  - `completed` → Post status: `published`
  - `failed` → Post status: `failed`

## API Endpoints

### AWS Backend Endpoints

| Method | Endpoint | Purpose | Auth Required |
|--------|----------|---------|---------------|
| POST | `/media/upload` | Request upload URL | Yes |
| PUT | `/media/{id}` | Update media status | Yes |
| POST | `/media/{id}/process` | Trigger AI processing | Yes |
| GET | `/media/{id}` | Get media details | Yes |
| DELETE | `/media/{id}` | Delete media | Yes |
| POST | `/webhooks/cloudflare/media-processed` | Handle AI completion | No (webhook) |

### Cloudflare Worker Endpoints

| Method | Endpoint | Purpose |
|--------|----------|---------|
| POST | `/upload-url` | Generate presigned upload URL |
| POST | `/process` | Trigger AI processing |
| GET | `/media/*` | Serve media files |
| GET | `/health` | Health check |

## Database Schema

### Media Table
```json
{
  "id": "string (primary key)",
  "fileName": "string",
  "fileType": "string",
  "fileSize": "number",
  "mediaType": "string",
  "userId": "string",
  "r2Key": "string",
  "bucketName": "string",
  "url": "string",
  "status": "pending_upload | uploaded | processing | completed | failed | rejected_nsfw",
  "postId": "string (optional)",
  "createdAt": "string",
  "updatedAt": "string",
  
  // AI Results (populated by webhook)
  "nsfw_analysis": "object",
  "videogame_analysis": "object", 
  "clip_tagging": "object",
  "processing_metadata": "object",
  "processing_completed_at": "string",
  "rejection_reason": "string",
  "error_message": "string"
}
```

## Cost Optimization

### Upload Costs
- ✅ **Direct R2 Upload**: No Lambda execution time during upload
- ✅ **No Double Transfer**: Files don't pass through Lambda
- ✅ **Presigned URLs**: Minimal Lambda execution for URL generation

### AI Processing Costs
- ✅ **NSFW Early Termination**: Saves ~70% on inappropriate content
- ✅ **On-Demand Processing**: Only runs when explicitly triggered
- ✅ **Conditional Models**: VideoGameBunny and CLIP only run on safe content

### Storage Costs
- ✅ **Lifecycle Policies**: Automatic cleanup of temporary files
- ✅ **Environment Separation**: Different retention policies per environment

## Security Features

### Authentication
- AWS API Gateway with Cognito authorizer for user endpoints
- Webhook signature verification for Cloudflare callbacks
- Environment-specific secrets and API keys

### Content Moderation
- Automatic NSFW detection with configurable thresholds
- Early rejection prevents inappropriate content from being published
- Detailed rejection reasons for moderation review

### Data Protection
- Secure presigned URLs with expiration
- CORS configuration for web uploads
- Rate limiting on all endpoints

## Error Handling

### Upload Failures
- Media status remains `pending_upload`
- Frontend can retry upload with same presigned URL (if not expired)
- Cleanup of orphaned media records

### Processing Failures
- Media status set to `failed`
- Error details stored in `error_message` field
- Webhook includes error information for debugging

### Webhook Failures
- Cloudflare Worker retries webhook delivery
- AWS Lambda logs all webhook processing errors
- Manual reprocessing available via `/process` endpoint

## Monitoring and Debugging

### AWS CloudWatch
- Lambda function logs and metrics
- API Gateway request/response logs
- DynamoDB operation metrics

### Cloudflare Analytics
- Worker execution metrics
- R2 storage usage and costs
- AI model usage and performance

### Health Checks
- `/health` endpoint on Cloudflare Worker
- AWS Lambda health monitoring
- End-to-end integration tests

## Testing

Run the integration tests:
```bash
cd backend
npm test -- tests/cloudflare-integration.test.js
```

Tests cover:
- Complete upload and processing workflow
- NSFW content rejection
- Webhook processing
- Error scenarios

## Environment Configuration

### AWS Environment Variables
```bash
CLOUDFLARE_WORKER_URL=https://gameflex-media-{environment}.workers.dev
CLOUDFLARE_WEBHOOK_SECRET=your-webhook-secret
```

### Cloudflare Environment Variables
```bash
NSFW_MODEL_NAME=@cf/falconsai/nsfw_image_detection
VIDEOGAME_MODEL_NAME=@cf/videogamebunny/videogame_image_recognition
CLIP_MODEL_NAME=@cf/openai/clip-vit-large-patch14-336
API_BASE_URL=https://{environment}.api.gameflex.io
WEBHOOK_SECRET=your-webhook-secret
```

## Deployment

1. **Deploy Cloudflare Infrastructure**:
   ```bash
   cd backend
   ./deploy-cloudflare.sh development
   ```

2. **Deploy AWS Backend**:
   ```bash
   npm run deploy:dev
   ```

3. **Configure Environment Variables**:
   - Set `CLOUDFLARE_WORKER_URL` in AWS
   - Set `API_BASE_URL` and `WEBHOOK_SECRET` in Cloudflare

4. **Test Integration**:
   ```bash
   npm test -- tests/cloudflare-integration.test.js
   ```

The integration provides a cost-effective, scalable solution for AI-powered media processing with automatic content moderation and rich gaming-focused tagging.
